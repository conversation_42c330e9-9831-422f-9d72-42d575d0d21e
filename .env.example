# Voice Assistant API Keys
NEXT_PUBLIC_VAPI_PUBLIC_KEY=your_vapi_public_key_here
ASSISTANT_ID=your_assistant_id_here

# EmailJS Configuration for Contact Form
# Get these values from https://www.emailjs.com/

# Your EmailJS Service ID
NEXT_PUBLIC_EMAILJS_SERVICE_ID=your_service_id

# Your EmailJS Template ID
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=your_template_id

# Your EmailJS Public Key
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=your_public_key

# Instructions:
# 1. Sign up at https://www.emailjs.com/
# 2. Create a new service (Gmail, Outlook, etc.)
# 3. Create an email template with the following variables:
#    - {{from_name}} - Sender's name
#    - {{from_email}} - Sender's email
#    - {{subject}} - Email subject
#    - {{message}} - Message content
#    - {{company}} - Sender's company (optional)
#    - {{to_email}} - Your email (<EMAIL>)
#    - {{reply_to}} - Reply-to email (sender's email)
# 4. Copy your Service ID, Template ID, and Public Key to .env.local
# 5. Replace the placeholder values above with your actual values

# Airtable API Configuration for Tech Stack
AIRTABLE_API_KEY=your_airtable_api_key
AIRTABLE_TECH_STACK_BASE_ID=your_airtable_base_id

# Add any other environment variables your app needs here
