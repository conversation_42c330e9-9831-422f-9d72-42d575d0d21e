{"name": "portfolio-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "analyze": "ANALYZE=true next build", "clean": "rm -rf .next", "check-bundle": "node scripts/check-bundle-size.js"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@vapi-ai/web": "^2.3.5", "airtable": "^0.12.2", "canvas-confetti": "^1.9.3", "clsx": "^2.1.1", "cmdk": "^1.1.1", "framer-motion": "^12.18.1", "gray-matter": "^4.0.3", "lucide-react": "^0.515.0", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "sharp": "^0.34.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.3", "@types/canvas-confetti": "^1.9.0", "@types/node": "^24.0.1", "@types/react": "^19", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-next": "^15.3.3", "postcss": "^8.5.6", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}