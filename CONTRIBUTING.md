# Contributing Guidelines

Thank you for your interest in contributing to the Hyperfox Portfolio project! This document provides guidelines and standards for contributing to ensure code quality and consistency.

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm 8+
- Git for version control
- VS Code (recommended) with suggested extensions

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd hyperfox-portfolio

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env.local

# Start development server
npm run dev
```

## 📋 Code Standards

### TypeScript Guidelines
- **Strict Mode**: All TypeScript strict checks are enabled
- **Type Safety**: Avoid `any` types, use proper type definitions
- **Interfaces**: Define interfaces for all props and data structures
- **Null Safety**: Handle null/undefined cases explicitly

```typescript
// ✅ Good
interface UserProps {
  name: string;
  email: string;
  isActive?: boolean;
}

// ❌ Avoid
function handleUser(user: any) {
  // ...
}
```

### Component Guidelines
- **Single Responsibility**: Each component should have one clear purpose
- **Composition**: Prefer composition over inheritance
- **Props Interface**: Always define TypeScript interfaces for props
- **Default Props**: Use default parameters instead of defaultProps

```typescript
// ✅ Component template
interface ComponentProps {
  title: string;
  isVisible?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export default function Component({ 
  title, 
  isVisible = true, 
  className,
  children 
}: ComponentProps) {
  return (
    <div className={cn("base-classes", className)}>
      {isVisible && <h2>{title}</h2>}
      {children}
    </div>
  );
}
```

### Styling Guidelines
- **Tailwind CSS**: Use utility classes for styling
- **Responsive Design**: Mobile-first approach
- **Theme Variables**: Use CSS custom properties for theming
- **Consistent Spacing**: Follow Tailwind spacing scale

```typescript
// ✅ Good styling patterns
className={cn(
  "base-classes",
  variant === "primary" && "primary-classes",
  isActive && "active-classes",
  className
)}

// ✅ Responsive design
className="text-sm md:text-base lg:text-lg"

// ✅ Theme-aware styling
className="bg-[hsl(var(--background))] text-[hsl(var(--foreground))]"
```

### File Naming Conventions
- **Components**: PascalCase (e.g., `Button.tsx`, `BlogCard.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useBlogData.ts`)
- **Utilities**: camelCase (e.g., `utils.ts`, `apiHelpers.ts`)
- **Types**: PascalCase (e.g., `BlogPost.ts`, `ApiResponse.ts`)

### Import Organization
```typescript
// 1. React and Next.js imports
import { useState, useEffect } from 'react';
import { NextRequest, NextResponse } from 'next/server';

// 2. Third-party library imports
import { motion } from 'framer-motion';
import { Mail, Github } from 'lucide-react';

// 3. Internal imports (absolute paths)
import { Button } from '@/components/ui';
import { useBlogData } from '@/hooks';
import { cn } from '@/lib/utils';

// 4. Relative imports
import './styles.css';
```

## 🧪 Testing Guidelines

### Component Testing
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Testing Requirements
- **Unit Tests**: All new components and hooks must have tests
- **Integration Tests**: Test component interactions
- **Accessibility Tests**: Ensure ARIA compliance
- **Performance Tests**: Monitor bundle size impact

## 🔧 Development Workflow

### Branch Naming
- **Feature**: `feature/add-blog-pagination`
- **Bug Fix**: `fix/contact-form-validation`
- **Hotfix**: `hotfix/security-patch`
- **Refactor**: `refactor/component-structure`

### Commit Messages
Follow conventional commit format:
```
type(scope): description

feat(blog): add pagination to blog posts
fix(contact): resolve form validation issues
docs(readme): update installation instructions
refactor(components): reorganize component structure
perf(images): optimize image loading
```

### Pull Request Process
1. **Create Feature Branch**: From main branch
2. **Implement Changes**: Follow coding standards
3. **Write Tests**: Ensure adequate test coverage
4. **Update Documentation**: Update relevant docs
5. **Run Quality Checks**: Ensure all checks pass
6. **Create Pull Request**: With clear description
7. **Code Review**: Address feedback
8. **Merge**: Squash and merge to main

### Quality Checks
Before submitting a PR, ensure:
```bash
# Type checking
npm run type-check

# Linting
npm run lint

# Formatting
npm run format

# Build
npm run build

# Bundle size check
npm run check-bundle
```

## 🎯 Accessibility Requirements

### WCAG 2.1 AA Compliance
- **Semantic HTML**: Use proper HTML elements
- **ARIA Labels**: Provide descriptive labels
- **Keyboard Navigation**: Ensure full keyboard support
- **Color Contrast**: Meet accessibility standards
- **Focus Management**: Proper focus indicators

```typescript
// ✅ Accessible component example
<button
  aria-label="Close dialog"
  aria-expanded={isOpen}
  aria-controls="dialog-content"
  onClick={handleClose}
>
  <X className="h-4 w-4" />
</button>
```

## 📊 Performance Guidelines

### Bundle Size
- **Monitor Impact**: Check bundle size for new dependencies
- **Code Splitting**: Use dynamic imports for large components
- **Tree Shaking**: Ensure imports are tree-shakeable
- **Bundle Analysis**: Regular bundle analysis

```typescript
// ✅ Dynamic import for code splitting
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <Spinner />,
  ssr: false,
});
```

### Optimization Techniques
```typescript
// ✅ Memoization for expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);

// ✅ Callback memoization
const handleClick = useCallback(() => {
  onClick(id);
}, [onClick, id]);

// ✅ Component memoization
const MemoizedComponent = memo(ExpensiveComponent);
```

## 🔒 Security Guidelines

### Input Validation
- **Sanitize Inputs**: Always sanitize user inputs
- **Validate Data**: Implement proper validation
- **Error Handling**: Don't expose sensitive information

```typescript
// ✅ Input sanitization
export function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

// ✅ Validation
const validation = validateContactForm(formData);
if (!validation.isValid) {
  setErrors(validation.errors);
  return;
}
```

## 📝 Documentation Requirements

### Code Documentation
- **Component Documentation**: JSDoc comments for components
- **Function Documentation**: Document complex functions
- **Type Documentation**: Document complex types
- **README Updates**: Update README for new features

```typescript
/**
 * BlogCard Component
 * 
 * Displays a blog post with title, excerpt, and metadata.
 * Supports hover animations and responsive design.
 * 
 * @example
 * ```tsx
 * <BlogCard post={blogPost} />
 * ```
 */
```

### Documentation Updates
- Update relevant documentation for new features
- Include usage examples
- Document breaking changes
- Update API documentation

## 🚫 Common Pitfalls to Avoid

### Performance
- ❌ Unnecessary re-renders
- ❌ Large bundle sizes
- ❌ Blocking the main thread
- ❌ Memory leaks

### Accessibility
- ❌ Missing ARIA labels
- ❌ Poor keyboard navigation
- ❌ Insufficient color contrast
- ❌ Missing alt text

### Code Quality
- ❌ Using `any` types
- ❌ Inconsistent naming
- ❌ Large components
- ❌ Tight coupling

## 🎉 Recognition

Contributors who follow these guidelines and make meaningful contributions will be recognized in the project's contributors section.

## 📞 Questions?

If you have questions about these guidelines:
1. Check the [documentation](./docs/)
2. Review existing code for examples
3. Create an issue for clarification

Thank you for contributing to making this project better! 🚀
