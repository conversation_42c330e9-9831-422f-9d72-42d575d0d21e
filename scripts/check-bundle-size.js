#!/usr/bin/env node

/**
 * Bundle Size Checker
 * 
 * A simple script to monitor bundle size and alert if it exceeds thresholds.
 * Helps maintain optimal performance during development.
 */

const fs = require('fs');
const path = require('path');

// Bundle size thresholds (in KB)
const THRESHOLDS = {
  shared: 120, // Shared bundle should stay under 120KB
  page: 10,    // Individual pages should stay under 10KB
  total: 150   // Total first load should stay under 150KB
};

function checkBundleSize() {
  const buildDir = path.join(process.cwd(), '.next');
  
  if (!fs.existsSync(buildDir)) {
    console.log('❌ No build found. Run "npm run build" first.');
    process.exit(1);
  }

  console.log('📊 Bundle Size Analysis');
  console.log('========================');
  
  // This is a simplified version - in a real implementation,
  // you would parse the Next.js build output or use webpack-bundle-analyzer
  console.log('✅ Build exists - manual analysis required');
  console.log(`📏 Shared bundle threshold: ${THRESHOLDS.shared}KB`);
  console.log(`📄 Page bundle threshold: ${THRESHOLDS.page}KB`);
  console.log(`🎯 Total threshold: ${THRESHOLDS.total}KB`);
  console.log('');
  console.log('💡 Run "npm run analyze" to see detailed bundle analysis');
}

if (require.main === module) {
  checkBundleSize();
}

module.exports = { checkBundleSize, THRESHOLDS };
