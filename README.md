# Thanmai Sai - Personal Portfolio

A modern, responsive portfolio website built with Next.js, TypeScript, and Tailwind CSS.

## Features

- Modern UI with multiple theme options
- Responsive design for all devices
- Fast performance with Next.js App Router
- TypeScript for type safety
- Tailwind CSS for styling
- Command menu for quick navigation
- SEO optimized
- PWA support
- Comprehensive theme system with 7 beautiful themes
- Smooth transitions between themes
- Persistent theme selection with localStorage

## Project Structure

```
portfolio/
├── public/              # Static assets
│   ├── fonts/           # Font files
│   ├── icons/           # Icon files
│   └── images/          # Image files
├── src/                 # Source code
│   ├── app/             # Next.js App Router pages
│   ├── components/      # React components
│   │   ├── common/      # Common components
│   │   ├── features/    # Feature-specific components
│   │   ├── layout/      # Layout components
│   │   └── ui/          # UI components
│   ├── content/         # Content files (MDX)
│   │   ├── experience/  # Experience content
│   │   ├── feed/        # Updates content
│   │   ├── projects/    # Projects content
│   │   └── blog/        # Blog content
│   ├── hooks/           # Custom React hooks
│   ├── lib/             # Utility libraries
│   │   ├── api/         # API utilities
│   │   ├── helpers/     # Helper functions
│   │   └── utils/       # Utility functions
│   ├── styles/          # Global styles
│   ├── types/           # TypeScript type definitions
│   └── config/          # Site configuration
└── ...                  # Config files
```

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Run the development server: `npm run dev`
4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Build and Deploy

1. Build the project: `npm run build`
2. Start the production server: `npm start`

## Recent Improvements

- **Enhanced Theme System** - Added multiple theme options with smooth transitions
- **Fixed Button Hover States** - Improved button interactions across all themes
- **Consistent Styling** - Ensured all components properly respond to theme changes
- **Optimized Performance** - Reduced unnecessary re-renders and improved load times
- **Comprehensive Accessibility** - Implemented full keyboard navigation, screen reader support, and focus management
- **Mobile Optimizations** - Enhanced sidebar and theme switching on mobile devices

## Theme System

The portfolio features a comprehensive theme system with 7 beautiful themes:

1. **Dark (Default)** - A refined dark theme with subtle blue tint
2. **Oceanic** - Soothing blue theme inspired by the ocean depths
3. **Midnight** - Deep blue midnight theme
4. **Synthwave** - Retro 80s neon vibes with softer contrast
5. **Terminal** - Soft green terminal theme
6. **Matrix** - Inspired by the Matrix movie
7. **Cyberpunk** - High-tech, low-life aesthetic

### Theme Features

- **Smooth Transitions** - Elegant transitions between themes
- **Persistent Selection** - Theme choice is saved in localStorage
- **Consistent Styling** - All components properly respond to theme changes
- **Accessibility** - Carefully selected colors for better readability
- **Multiple Access Points** - Change themes via sidebar, command menu, or keyboard shortcuts

### Implementation Details

- CSS variables for all colors and design tokens
- HSL color format for better color manipulation
- Context API for theme state management
- Optimized for performance with minimal re-renders

## Recent Accessibility Improvements

### Command Menu Enhancements
- **Keyboard Navigation**: Added arrow key navigation with visual indicators
- **Auto-Scrolling**: Implemented automatic scrolling to reveal off-screen options
- **Focus Management**: Improved focus handling when opening/closing the menu
- **Visual Feedback**: Added clear visual indicators for the currently selected item

### Tab Navigation Improvements
- **Position Memory**: Tab position is now maintained across page navigation
- **Focus History**: Implemented a focus history system to remember previous focus points
- **Seamless Experience**: Tab navigation now feels natural and predictable

### Accessibility Attribute Fixes
- **Automatic Repairs**: Added system to automatically fix missing accessibility attributes
- **Icon Buttons**: Ensured all icon buttons have proper accessible names
- **Form Elements**: Added labels to all form elements
- **Images**: Verified all images have appropriate alt text

## Accessibility Features

This portfolio includes comprehensive accessibility features to ensure it's usable by everyone, regardless of their abilities or how they access the web.

### Keyboard Navigation

#### Tab Navigation
- **Persistent Tab Position**: Tab navigation maintains its position across page loads and navigation
- **Focus Management**: Proper focus management ensures focus is restored when navigating between pages
- **Focus Indicators**: Clear visual indicators show which element is currently focused
- **Skip Links**: Skip navigation links allow keyboard users to bypass repetitive navigation

#### Command Menu (CMD+K)
- **Arrow Key Navigation**: Navigate through options using up/down arrow keys
- **Auto-Scrolling**: Menu automatically scrolls to reveal options outside the visible area
- **Visual Indicators**: Clear visual highlighting shows the currently selected option
- **Enter Key Selection**: Select options by pressing Enter
- **Escape Key**: Close the menu by pressing Escape

#### Keyboard Shortcuts
- **Page Navigation**: Use number keys 1-8 to navigate to different pages
- **Special Shortcuts**:
  - `Ctrl+G`: Go to Guestbook
  - `Ctrl+C`: Go to Contact
  - `/` or `?`: Show keyboard shortcuts help

### Screen Reader Support

- **Semantic HTML**: Proper heading hierarchy and semantic structure
- **ARIA Attributes**: Appropriate ARIA roles, states, and properties
- **Accessible Names**: All interactive elements have accessible names
- **Alternative Text**: Images have appropriate alt text

### Visual Accessibility

- **Color Contrast**: Sufficient color contrast ratios for text and UI elements
- **Theme Options**: Multiple theme options to accommodate different visual preferences
- **Responsive Design**: Fully responsive layout that works on all screen sizes
- **Text Scaling**: Text scales properly when browser font size is increased

### Focus Management Components

#### TabFocusManager
Manages tab focus throughout the application:
- Tracks focus history for maintaining tab position
- Ensures all focusable elements have unique IDs
- Restores focus position when tabbing from document body

#### FocusManagement Hook
Enhances accessibility by fixing missing attributes:
- Adds accessible names to icon buttons and links
- Ensures images have alt text
- Adds labels to form elements without explicit labels

## Technologies Used

- Next.js 14
- TypeScript
- React
- Tailwind CSS
- shadcn/ui
- Framer Motion
- Lucide Icons
- MDX for content
- Context API for state management

## Accessibility Standards

This portfolio follows the Web Content Accessibility Guidelines (WCAG) 2.1 Level AA standards, ensuring that:

- All content is perceivable to users of assistive technologies
- All functionality is operable through a keyboard
- Content is understandable and navigable
- Content is robust enough to be interpreted by a wide variety of user agents

## Conclusion

This portfolio website demonstrates a commitment to both modern design and inclusive accessibility. By implementing comprehensive keyboard navigation, screen reader support, and focus management, it ensures that all users can access and interact with the content, regardless of their abilities or how they access the web.

The recent improvements to the Command Menu and tab navigation system showcase how accessibility can be seamlessly integrated into the user experience, making the website more usable for everyone while maintaining its visual appeal and performance.
