{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "files.associations": {"*.css": "tailwindcss"}, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "tailwindCSS.experimental.classRegex": [["cn\\(([^)]*)\\)", "'([^']*)'"], ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]], "typescript.preferences.includePackageJsonAutoImports": "on"}