// Preload data for ultra-fast loading
import { EnhancedBlogPost } from '@/types/content'
import { TechCategory } from '@/types/techStack'

interface PreloadCache<T> {
  data: T[]
  timestamp: number
  loading: boolean
}

// Global caches that persist across components
let globalCache: PreloadCache<EnhancedBlogPost> | null = null
let techStackCache: PreloadCache<TechCategory> | null = null
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Preload function for blog data
export async function preloadBlogData(): Promise<EnhancedBlogPost[]> {
  // Return cached data if still valid
  if (globalCache && Date.now() - globalCache.timestamp < CACHE_DURATION) {
    return globalCache.data
  }

  // Prevent multiple simultaneous requests
  if (globalCache?.loading) {
    // Wait for ongoing request
    return new Promise((resolve) => {
      const checkCache = () => {
        if (globalCache && !globalCache.loading) {
          resolve(globalCache.data)
        } else {
          setTimeout(checkCache, 50)
        }
      }
      checkCache()
    })
  }

  try {
    // Mark as loading
    globalCache = { data: [], timestamp: 0, loading: true }

    const response = await fetch('/api/blog', {
      headers: {
        'Cache-Control': 'max-age=300', // 5 minute browser cache
        'Priority': 'high' // High priority request
      }
    })

    if (!response.ok) {
      throw new Error('Failed to fetch')
    }

    const result = await response.json()
    
    if (result.success) {
      globalCache = {
        data: result.data,
        timestamp: Date.now(),
        loading: false
      }
      return result.data
    } else {
      throw new Error(result.error || 'API error')
    }
  } catch (error) {
    // Reset loading state on error
    if (globalCache) {
      globalCache.loading = false
    }
    console.error('Preload failed:', error)
    return []
  }
}

// Get cached data synchronously (for instant access)
export function getCachedBlogData(): EnhancedBlogPost[] | null {
  if (globalCache && Date.now() - globalCache.timestamp < CACHE_DURATION) {
    return globalCache.data
  }
  return null
}

// Check if data is currently loading
export function isBlogDataLoading(): boolean {
  return globalCache?.loading || false
}

// Start preloading immediately when this module is imported
if (typeof window !== 'undefined') {
  // Preload on page load
  preloadBlogData()
  
  // Preload on page visibility change (when user comes back to tab)
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      preloadBlogData()
    }
  })
}
