import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { Project } from '@/types/content'

const projectsDirectory = path.join(process.cwd(), 'src/content/projects')

// Get all projects
export function getAllProjects(): Project[] {
  // Check if directory exists
  if (!fs.existsSync(projectsDirectory)) {
    return []
  }

  const fileNames = fs.readdirSync(projectsDirectory)
  const allProjectsData = fileNames
    .filter((fileName) => fileName.endsWith('.mdx'))
    .map((fileName) => {
      const slug = fileName.replace(/\.mdx$/, '')
      const fullPath = path.join(projectsDirectory, fileName)
      const fileContents = fs.readFileSync(fullPath, 'utf8')
      const { data, content } = matter(fileContents)

      return {
        slug,
        title: data.title,
        shortDescription: data.shortDescription,
        date: data.date,
        tags: data.tags || [],
        imageUrl: data.imageUrl,
        demoUrl: data.demoUrl,
        sourceUrl: data.sourceUrl,
        content,
      } as Project
    })

  // Sort projects by date
  return allProjectsData.sort((a, b) => {
    if (a.date < b.date) {
      return 1
    } else {
      return -1
    }
  })
}

// Get a single project by slug
export function getProjectBySlug(slug: string): Project | null {
  const fullPath = path.join(projectsDirectory, `${slug}.mdx`)
  
  if (!fs.existsSync(fullPath)) {
    return null
  }

  const fileContents = fs.readFileSync(fullPath, 'utf8')
  const { data, content } = matter(fileContents)

  return {
    slug,
    title: data.title,
    shortDescription: data.shortDescription,
    date: data.date,
    tags: data.tags || [],
    imageUrl: data.imageUrl,
    demoUrl: data.demoUrl,
    sourceUrl: data.sourceUrl,
    content,
  } as Project
}

// Similar functions can be implemented for experience, blog posts, and feed items
