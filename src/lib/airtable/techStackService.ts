import Airtable, { FieldSet, Record, Records } from 'airtable';
import { AIRTABLE_TECH_STACK_FIELDS } from './techStackSchema';
import { Tool, TechCategory, TechStackRecord } from '@/types/techStack';

// Initialize Airtable
const airtableBase = new Airtable({ 
  apiKey: process.env.AIRTABLE_API_KEY 
}).base(process.env.AIRTABLE_TECH_STACK_BASE_ID || '');

// Cache for tech stack data (5-minute TTL)
interface CacheItem {
  data: TechCategory[];
  timestamp: number;
}

const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds
let cache: CacheItem | null = null;

/**
 * Fetches all tech stack data from Airtable
 * Organizes it by category, with tools nested under each category
 * Implements caching with a 5-minute TTL
 * @param forceRefresh - If true, bypasses the cache
 * @returns Promise with tech stack data
 */
export async function fetchTechStackData(forceRefresh = false): Promise<TechCategory[]> {
  try {
    // Check if we have valid cached data
    const now = Date.now();
    if (cache && !forceRefresh && (now - cache.timestamp < CACHE_TTL)) {
      return cache.data;
    }
    
    // Fetch fresh data
    const records = await fetchTechStackRecords();
    
    // Process the data
    const techStackData = processTechStackRecords(records);
    
    // Update the cache
    cache = {
      data: techStackData,
      timestamp: now
    };
    
    return techStackData;
  
  } catch (error) {
    console.error('Error fetching tech stack data:', error);
    
    // If we have cached data, return it even if it's stale
    if (cache) {
      return cache.data;
    }
    
    throw error;
  }
}

/**
 * Processes raw records from Airtable into categorized tech stack data
 */
function processTechStackRecords(records: TechStackRecord[]): TechCategory[] {
  // Group records by category
  const groupedByCategory = records.reduce<{ [key: string]: TechStackRecord[] }>(
    (acc, record) => {
      const category = record.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(record);
      return acc;
    },
    {}
  );
  
  // Convert grouped records to TechCategory array
  const techStackData: TechCategory[] = Object.keys(groupedByCategory).map(categoryName => {
    const categoryRecords = groupedByCategory[categoryName];
    // Use the first record in each category to get the category metadata
    const firstCategoryRecord = categoryRecords[0];
    
    // Map records to Tool interface
    const tools: Tool[] = categoryRecords.map((record: TechStackRecord) => {
      // Convert comma-separated tags to array
      const tagsList = record.tags
        ? record.tags
            .split(',')
            .map((tag: string) => tag.trim())
            .filter(Boolean)
        : [];
      
      return {
        name: record.name,
        description: record.description,
        tags: tagsList,
        website: record.website,
        level: record.level as 'Expert' | 'Advanced' | 'Intermediate',
        status: record.status as 'Core Skill' | 'Learning',
      };
    });
    
    // Sort tools by display order or alphabetically
    tools.sort((a, b) => {
      const recordA = categoryRecords.find((r: TechStackRecord) => r.name === a.name);
      const recordB = categoryRecords.find((r: TechStackRecord) => r.name === b.name);
      
      // If both have display orders, sort by that
      if (recordA?.displayOrder !== undefined && recordB?.displayOrder !== undefined) {
        return recordA.displayOrder - recordB.displayOrder;
      }
      
      // Otherwise sort alphabetically
      return a.name.localeCompare(b.name);
    });
    
    return {
      category: categoryName,
      icon: firstCategoryRecord.categoryIcon,
      color: firstCategoryRecord.categoryColor,
      description: firstCategoryRecord.categoryDescription,
      tools: tools,
    };
  });
  
  // Sort categories by category order
  techStackData.sort((a, b) => {
    const recordsA = groupedByCategory[a.category];
    const recordsB = groupedByCategory[b.category];
    
    if (recordsA && recordsB) {
      const categoryOrderA = recordsA[0].categoryOrder || 0;
      const categoryOrderB = recordsB[0].categoryOrder || 0;
      return categoryOrderA - categoryOrderB;
    }
    
    return 0;
  });
  
  return techStackData;
}

/**
 * Fetches all tech stack records from Airtable
 * Implements robust error handling and data validation
 */
async function fetchTechStackRecords(): Promise<TechStackRecord[]> {
  const fields = AIRTABLE_TECH_STACK_FIELDS;

  // Validate environment variables
  if (!process.env.AIRTABLE_API_KEY || !process.env.AIRTABLE_TECH_STACK_BASE_ID) {
    throw new Error('Airtable API key or base ID is missing');
  }

  return new Promise<TechStackRecord[]>((resolve, reject) => {
    const records: TechStackRecord[] = [];
    const startTime = Date.now();

    airtableBase('Tech Stack')
      .select({
        view: 'Grid view',
      })
      .eachPage(
        (recordsPage: Records<FieldSet>, fetchNextPage: () => void) => {
          recordsPage.forEach((record: Record<FieldSet>) => {
            // Extract data with default values for safety
            records.push({
              id: record.id,
              name: record.get(fields.NAME) as string || 'Unnamed Tool',
              description: record.get(fields.DESCRIPTION) as string || '',
              website: record.get(fields.WEBSITE) as string || '',
              level: record.get(fields.LEVEL) as string || 'Intermediate',
              status: record.get(fields.STATUS) as string || 'Core Skill',
              category: record.get(fields.CATEGORY) as string || 'Uncategorized',
              tags: record.get(fields.TAGS) as string || '',
              categoryIcon: record.get(fields.CATEGORY_ICON) as string || '',
              categoryColor: record.get(fields.CATEGORY_COLOR) as string || '#808080',
              categoryDescription: record.get(fields.CATEGORY_DESCRIPTION) as string || '',
              displayOrder: record.get(fields.DISPLAY_ORDER) as number || 999,
              categoryOrder: record.get(fields.CATEGORY_ORDER) as number || 999,
            });
          });

          fetchNextPage();
        },
        (err: Error) => {
          if (err) {
            console.error('Airtable API Error:', err);
            reject(err);
          } else {
            // For performance monitoring, but not in production logs
            if (process.env.NODE_ENV === 'development') {
              const duration = Date.now() - startTime;
              console.log(`Fetched ${records.length} records in ${duration}ms`);
            }
            resolve(records);
          }
        }
      );
  });
}
