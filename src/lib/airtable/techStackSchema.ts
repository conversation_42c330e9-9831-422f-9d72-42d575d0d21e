/**
 * Airtable Schema for Tech Stack
 * 
 * This schema defines the field mappings for the Tech Stack table in Airtable.
 * We use a single table approach for simplicity, with each technology storing its
 * own category metadata.
 * 
 * Table Structure:
 * - Each record represents a technology/tool
 * - Records contain both tool-specific fields and category metadata
 * - This allows for flexibility while keeping the schema simple
 *
 * Expected Airtable Structure:
 * - Table Name: Tech Stack
 * - View Name: Grid view
 *
 * Required environment variables:
 * - AIRTABLE_API_KEY: Your Airtable API key
 * - AIRTABLE_TECH_STACK_BASE_ID: The Base ID containing your Tech Stack table
 */

/**
 * Field mapping constants for Airtable API integration
 * Maps our application field names to the actual field names in Airtable
 */
export const AIRTABLE_TECH_STACK_FIELDS = {
  // Technology fields
  NAME: 'Name',                 // Required - Name of the tool/technology
  DESCRIPTION: 'Description',    // Tool description
  WEBSITE: 'Website',           // Link to official website
  LEVEL: 'Level',               // Experience level: 'Expert', 'Advanced', 'Intermediate'
  STATUS: 'Status',             // Usage status: 'Core Skill', 'Learning'
  TAGS: 'Tags',                 // Comma-separated list of tags
  DISPLAY_ORDER: 'Display Order', // Number for sorting tools within a category
  
  // Category fields - These fields should be identical for all tools in the same category
  CATEGORY: 'Category',         // Required - Name of the category
  CATEGORY_ICON: 'Category Icon', // Icon name (from Lucide icons library)
  CATEGORY_COLOR: 'Category Color', // Color code for the category
  CATEGORY_DESCRIPTION: 'Category Description', // Description of the category
  CATEGORY_ORDER: 'Category Order' // Number for sorting categories
};
