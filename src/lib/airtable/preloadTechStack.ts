// Preload function for tech stack data
import { TechCategory } from '@/types/techStack';

interface PreloadCache<T> {
  data: T[];
  timestamp: number;
  loading: boolean;
}

// Global cache for tech stack data
let techStackCache: PreloadCache<TechCategory> | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Preload tech stack data for ultra-fast loading
 * This function fetches tech stack data and caches it globally
 */
export async function preloadTechStackData(): Promise<TechCategory[]> {
  // Return cached data if still valid
  if (techStackCache && Date.now() - techStackCache.timestamp < CACHE_DURATION) {
    return techStackCache.data;
  }

  // Prevent multiple simultaneous requests
  if (techStackCache?.loading) {
    // Wait for ongoing request
    return new Promise((resolve) => {
      const checkCache = () => {
        if (techStackCache && !techStackCache.loading) {
          resolve(techStackCache.data);
        } else {
          setTimeout(checkCache, 50);
        }
      };
      checkCache();
    });
  }

  try {
    // Mark as loading
    techStackCache = { data: [], timestamp: 0, loading: true };

    const response = await fetch('/api/stack', {
      headers: {
        'Cache-Control': 'max-age=300', // 5 minute browser cache
        'Priority': 'high' // High priority request
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch tech stack data: ${response.status}`);
    }

    const result = await response.json();
    
    // Update cache with fresh data
    techStackCache = {
      data: result.techStackData || [],
      timestamp: Date.now(),
      loading: false
    };
    
    return result.techStackData || [];
  } catch (error) {
    // Reset loading state on error
    if (techStackCache) {
      techStackCache.loading = false;
    }
    console.error('Tech stack preload failed:', error);
    return [];
  }
}

// Get cached tech stack data synchronously (for instant access)
export function getCachedTechStackData(): TechCategory[] | null {
  if (techStackCache && Date.now() - techStackCache.timestamp < CACHE_DURATION) {
    return techStackCache.data;
  }
  return null;
}

// Check if tech stack data is currently loading
export function isTechStackDataLoading(): boolean {
  return techStackCache?.loading || false;
}

// Start preloading immediately when this module is imported
if (typeof window !== 'undefined') {
  // Use requestIdleCallback for non-critical preloading
  const preloadFn = window.requestIdleCallback || setTimeout;
  preloadFn(() => {
    preloadTechStackData();
  });
}
