import emailjs from '@emailjs/browser'

// EmailJS configuration
const EMAILJS_SERVICE_ID = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || 'your_service_id'
const EMAILJS_TEMPLATE_ID = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID || 'your_template_id'
const EMAILJS_PUBLIC_KEY = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || 'your_public_key'

export interface EmailData {
  name: string
  email: string
  message: string
  subject?: string
  company?: string
}

export interface EmailResponse {
  success: boolean
  message: string
  error?: string
}

/**
 * Send email using EmailJS
 */
export async function sendEmail(data: EmailData): Promise<EmailResponse> {
  try {
    // Initialize EmailJS
    emailjs.init(EMAILJS_PUBLIC_KEY)

    // Prepare template parameters
    const templateParams = {
      from_name: data.name,
      from_email: data.email,
      subject: data.subject || `New message from ${data.name}`,
      message: data.message,
      company: data.company || 'Not specified',
      to_email: '<EMAIL>', // Your email
      reply_to: data.email,
    }

    // Send email
    const response = await emailjs.send(
      EMAILJS_SERVICE_ID,
      EMAILJS_TEMPLATE_ID,
      templateParams,
      EMAILJS_PUBLIC_KEY
    )

    if (response.status === 200) {
      return {
        success: true,
        message: 'Email sent successfully!'
      }
    } else {
      throw new Error(`EmailJS returned status: ${response.status}`)
    }
  } catch (error) {
    return {
      success: false,
      message: 'Failed to send email',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Validate email configuration
 */
export function isEmailConfigured(): boolean {
  return !!(
    EMAILJS_SERVICE_ID && 
    EMAILJS_TEMPLATE_ID && 
    EMAILJS_PUBLIC_KEY &&
    EMAILJS_SERVICE_ID !== 'your_service_id' &&
    EMAILJS_TEMPLATE_ID !== 'your_template_id' &&
    EMAILJS_PUBLIC_KEY !== 'your_public_key'
  )
}

/**
 * Get email configuration status
 */
export function getEmailConfigStatus() {
  return {
    configured: isEmailConfigured(),
    serviceId: !!EMAILJS_SERVICE_ID && EMAILJS_SERVICE_ID !== 'your_service_id',
    templateId: !!EMAILJS_TEMPLATE_ID && EMAILJS_TEMPLATE_ID !== 'your_template_id',
    publicKey: !!EMAILJS_PUBLIC_KEY && EMAILJS_PUBLIC_KEY !== 'your_public_key',
  }
}
