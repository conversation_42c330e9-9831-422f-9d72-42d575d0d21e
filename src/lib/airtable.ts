const AIRTABLE_BASE_ID = 'appLjChS6nQJQv7qG';
const AIRTABLE_TABLE_ID = 'tblOAaswB0JRhhGgT'; // Use the table ID from your URL
const AIRTABLE_API_KEY = process.env.AIRTABLE_API_KEY;

export interface Update {
  id: string;
  date: string;
  title: string;
  content: string;
  type: string;
  images: string[];
  achievement: boolean;
  link?: string;
}

export async function getUpdates(): Promise<Update[]> {
  try {
    if (!AIRTABLE_API_KEY) {
      console.error('AIRTABLE_API_KEY is not set');
      return [];
    }

    const response = await fetch(
      `https://api.airtable.com/v0/${AIRTABLE_BASE_ID}/${AIRTABLE_TABLE_ID}?sort%5B0%5D%5Bfield%5D=Date&sort%5B0%5D%5Bdirection%5D=desc`,
      {
        headers: {
          Authorization: `Bearer ${AIRTABLE_API_KEY}`,
          'Content-Type': 'application/json',
        },
        next: { revalidate: 300 }, // Cache for 5 minutes
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Airtable API Error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText,
      });
      throw new Error(`Failed to fetch updates: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    console.log('Airtable data sample:', data.records[0]?.fields);

    return data.records.map((record: any) => ({
      id: record.id,
      date: record.fields.Date,
      title: record.fields.Title || 'Untitled Update',
      content: record.fields.Content || '',
      type: record.fields.Type || 'general',
      images: record.fields.Image ? record.fields.Image.map((img: any) => img.url) : [],
      achievement: record.fields.Achievement || false,
      link: record.fields.Link || null,
    }));
  } catch (error) {
    console.error('Error fetching updates:', error);
    return [];
  }
}
