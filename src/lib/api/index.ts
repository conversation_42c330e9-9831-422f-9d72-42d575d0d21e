/**
 * API Utilities Index
 * 
 * Centralized exports for all API-related utilities, types, and helpers.
 * Provides a clean interface for API development and maintenance.
 */

// Core API utilities
export * from './types';
export * from './response';
export * from './validation';
export * from './middleware';

// External API integrations
export * from './medium';

// Re-export commonly used functions for convenience
export { 
  createSuccessResponse, 
  createErrorResponse, 
  createValidationErrorResponse,
  createRateLimitResponse,
  createNotFoundResponse,
  createOptionsResponse 
} from './response';

export { 
  validateContactForm, 
  sanitizeContactForm, 
  validateEmail, 
  sanitizeInput 
} from './validation';

export { 
  getRequestContext, 
  checkRateLimit, 
  RATE_LIMIT_CONFIGS,
  parseJsonBody,
  getQueryParams,
  logRequest 
} from './middleware';

export { ApiErrorCode } from './types';
export type { ApiResponse, ApiError, RequestContext, ValidationResult } from './types';
