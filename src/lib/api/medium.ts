import { MediumArticle, BlogPost } from '@/types/content'
import { blogConfig } from '@/config/blog'

// Get RSS URL from config
const MEDIUM_RSS_URL = blogConfig.medium.userFeedUrl

// Cache duration from config
const CACHE_DURATION = blogConfig.medium.cacheDuration

interface CacheEntry {
  data: MediumArticle[]
  timestamp: number
}

// In-memory cache (in production, consider using Redis or similar)
let cache: CacheEntry | null = null



/**
 * Parse RSS XML directly (fallback method)
 */
async function parseRSSDirectly(rssUrl: string): Promise<MediumArticle[]> {
  try {
    // Try multiple CORS proxies
    const proxies = [
      `https://api.allorigins.win/get?url=${encodeURIComponent(rssUrl)}`,
      `https://corsproxy.io/?${encodeURIComponent(rssUrl)}`,
      `https://cors-anywhere.herokuapp.com/${rssUrl}`
    ]

    for (const proxyUrl of proxies) {
      try {
        const response = await fetch(proxyUrl, {
          headers: {
            'Accept': 'application/json, text/xml, application/xml',
            'User-Agent': 'Mozilla/5.0 (compatible; RSS Reader)',
          },
        })

        if (!response.ok) continue

        let xmlText = ''
        const contentType = response.headers.get('content-type') || ''

        if (contentType.includes('application/json')) {
          const data = await response.json()
          xmlText = data.contents || data.data || ''
        } else {
          xmlText = await response.text()
        }

        if (xmlText && xmlText.includes('<rss')) {
          return parseXMLToBlogPosts(xmlText)
        }
      } catch {
        continue
      }
    }

    throw new Error('All proxies failed')
  } catch {
    return []
  }
}

/**
 * Parse XML content to blog posts
 */
function parseXMLToBlogPosts(xmlText: string): MediumArticle[] {
  try {
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(xmlText, 'text/xml')

    const items = xmlDoc.querySelectorAll('item')
    const blogPosts: MediumArticle[] = []

    items.forEach((item) => {
      const title = item.querySelector('title')?.textContent || ''
      const link = item.querySelector('link')?.textContent || ''
      const pubDate = item.querySelector('pubDate')?.textContent || ''
      const description = item.querySelector('description')?.textContent || ''
      const guid = item.querySelector('guid')?.textContent || ''
      const creator = item.querySelector('creator')?.textContent || 'HyperFox'

      // Extract categories/tags
      const categories: string[] = []
      item.querySelectorAll('category').forEach((cat) => {
        const categoryText = cat.textContent
        if (categoryText) categories.push(categoryText)
      })

      // Clean description
      const cleanDescription = description
        .replace(/<[^>]*>/g, '')
        .replace(/&[^;]+;/g, ' ')
        .trim()
        .substring(0, 200) + (description.length > 200 ? '...' : '')

      // Extract thumbnail
      const thumbnail = extractImageFromContent(description)

      if (title && link) {
        blogPosts.push({
          title: title.trim(),
          link: link.trim(),
          pubDate: pubDate ? new Date(pubDate).toISOString() : new Date().toISOString(),
          description: cleanDescription,
          content: description,
          guid,
          categories,
          thumbnail,
          author: creator,
          readTime: estimateReadTime(description)
        })
      }
    })

    return blogPosts.sort((a: MediumArticle, b: MediumArticle) => new Date(b.pubDate).getTime() - new Date(a.pubDate).getTime())
  } catch {
    return []
  }
}

/**
 * Fetch Medium blog posts with caching and multiple fallback methods
 */
export async function fetchMediumBlogPosts(): Promise<MediumArticle[]> {
  try {
    // Check cache first
    if (cache && Date.now() - cache.timestamp < CACHE_DURATION) {
      return cache.data
    }

    let blogPosts: MediumArticle[] = []

    // Method 1: Try RSS2JSON API
    try {
      const proxyUrl = `https://api.rss2json.com/v1/api.json?rss_url=${encodeURIComponent(MEDIUM_RSS_URL)}&count=50`

      const response = await fetch(proxyUrl, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (compatible; RSS Reader)',
        },
      })

      if (response.ok) {
        const data = await response.json()

        if (data.status === 'ok' && data.items && data.items.length > 0) {

          blogPosts = data.items.map((item: {
            title?: string;
            link?: string;
            pubDate?: string;
            description?: string;
            content?: string;
            guid?: string;
            categories?: string[];
            thumbnail?: string;
            author?: string;
          }) => {
            const cleanDescription = item.description
              ? item.description.replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, ' ').trim().substring(0, 200) + '...'
              : ''

            const thumbnail = item.thumbnail || extractImageFromContent(item.content || item.description || '')
            const categories = Array.isArray(item.categories) ? item.categories : []

            return {
              title: item.title || 'Untitled',
              link: item.link || '',
              pubDate: item.pubDate ? new Date(item.pubDate).toISOString() : new Date().toISOString(),
              description: cleanDescription,
              content: item.content || item.description || '',
              guid: item.guid || item.link || '',
              categories: categories,
              thumbnail: thumbnail,
              author: item.author || 'HyperFox',
              readTime: estimateReadTime(item.content || item.description || '')
            }
          })
        }
      }
    } catch {
      // RSS2JSON failed, continue to fallback method
    }

    // Method 2: If RSS2JSON failed, try direct RSS parsing
    if (blogPosts.length === 0) {
      blogPosts = await parseRSSDirectly(MEDIUM_RSS_URL)
    }

    // Filter and sort blog posts
    const validBlogPosts = blogPosts
      .filter((blogPost: MediumArticle) => blogPost.title && blogPost.link)
      .sort((a: MediumArticle, b: MediumArticle) => new Date(b.pubDate).getTime() - new Date(a.pubDate).getTime())

    if (validBlogPosts.length > 0) {
      // Update cache
      cache = {
        data: validBlogPosts,
        timestamp: Date.now()
      }

      return validBlogPosts
    } else {
      throw new Error('No blog posts found from any method')
    }

  } catch {
    // Return cached data if available, even if expired
    if (cache && cache.data.length > 0) {
      return cache.data
    }

    // As a last resort, return hardcoded blog posts based on your RSS feed
    return getHardcodedBlogPosts()
  }
}

/**
 * Hardcoded blog posts as final fallback (based on your actual RSS feed)
 */
function getHardcodedBlogPosts(): MediumArticle[] {
  return [
    {
      title: "GitSwitching Gears: How to Seamlessly Manage Multiple Git Accounts on a Mac",
      link: "https://medium.com/@hyperfox_/gitswitching-gears-how-to-seamlessly-manage-multiple-git-accounts-on-a-mac-b1e581704a58",
      pubDate: "2025-06-12T20:01:27.538Z",
      description: "Ever tried pushing to a personal GitHub repo, only to accidentally commit with your office credentials? You're not alone. If you're working on both work and personal projects on the same MacBook, you need a clean way to manage multiple Git identities...",
      content: "Ever tried pushing to a personal GitHub repo, only to accidentally commit with your office credentials? You're not alone. If you're working on both work and personal projects on the same MacBook, you need a clean way to manage multiple Git identities.",
      guid: "https://medium.com/p/b1e581704a58",
      categories: ["Git", "Mac", "Development"],
      thumbnail: "https://cdn-images-1.medium.com/max/1024/1*_DZf38iJbTwUu14UM_KYFw.jpeg",
      author: "HyperFox",
      readTime: "8 min read"
    },
    {
      title: "Run Cursor AI for Free with Open-Source LLM",
      link: "https://medium.com/@hyperfox_/run-cursor-ai-for-free-with-open-source-llm-55396c1411b1",
      pubDate: "2025-02-08T08:05:11.729Z",
      description: "Ever wanted to harness the power of Cursor AI without breaking the bank? You're in luck! This guide will walk you through setting up Cursor AI locally using open-source Large Language Models (LLMs)...",
      content: "Ever wanted to harness the power of Cursor AI without breaking the bank? You're in luck! This guide will walk you through setting up Cursor AI locally using open-source Large Language Models (LLMs).",
      guid: "https://medium.com/p/55396c1411b1",
      categories: ["AI", "Cursor", "LLM", "Development"],
      thumbnail: "https://cdn-images-1.medium.com/max/686/1*dmLoFxzYUeLhKMZhMWfJWg.png",
      author: "HyperFox",
      readTime: "12 min read"
    },
    {
      title: "Drowning in Digital Debris?",
      link: "https://medium.com/@hyperfox_/drowning-in-digital-debris-93ec871d424c",
      pubDate: "2024-06-17T13:10:44.478Z",
      description: "Is Spam Taking Over Your Life? Stop the Madness and Silence It All (Email, Text, Calls). Does your phone sound like a swarm of angry bees and your inbox resemble a overflowing dumpster?...",
      content: "Is Spam Taking Over Your Life? Stop the Madness and Silence It All (Email, Text, Calls)",
      guid: "https://medium.com/p/93ec871d424c",
      categories: ["Productivity", "Digital Wellness", "Tips"],
      thumbnail: "https://cdn-images-1.medium.com/max/500/0*bU46u2olVjkDDoKq",
      author: "HyperFox",
      readTime: "10 min read"
    },
    {
      title: "What can you accomplish with Mac shortcuts?",
      link: "https://medium.com/@hyperfox_/what-can-you-accomplish-with-mac-shortcuts-27ed35f5ff25",
      pubDate: "2024-04-02T20:07:15.802Z",
      description: "Command Your Mac: The Shortcut Guide Every Fan Needs. As a developer, I think using shortcuts is an easier way of accomplishing tasks than using graphical user interfaces...",
      content: "Command Your Mac: The Shortcut Guide Every Fan Needs",
      guid: "https://medium.com/p/27ed35f5ff25",
      categories: ["Mac", "Productivity", "Shortcuts"],
      thumbnail: "https://cdn-images-1.medium.com/max/1024/1*RWc-JR_xctfLok710xB9ug.jpeg",
      author: "HyperFox",
      readTime: "6 min read"
    },
    {
      title: "The Great Browser Brawl: Who Rules the Web Arena?",
      link: "https://medium.com/@hyperfox_/the-great-browser-brawl-who-rules-the-web-arena-d3a2636a38bb",
      pubDate: "2024-01-25T17:34:24.878Z",
      description: "Pick your browser wisely, your digital life depends on it! We're about to engage in the ultimate browser war in the virtual colosseum!...",
      content: "Pick your browser wisely, your digital life depends on it!",
      guid: "https://medium.com/p/d3a2636a38bb",
      categories: ["Browsers", "Technology", "Comparison"],
      thumbnail: "https://cdn-images-1.medium.com/max/1024/1*Vm4VJ9rK3acjqMNQGQs6Iw.png",
      author: "HyperFox",
      readTime: "8 min read"
    },
    {
      title: "Supercharge Your Developer Experience: Essential Apps for an Elevated Mac Setup",
      link: "https://medium.com/@hyperfox_/supercharge-your-developer-experience-essential-apps-for-an-elevated-mac-setup-5eaec55334d1",
      pubDate: "2024-01-23T07:51:30.867Z",
      description: "Optimize Your Workflow with These Must-Have Tools for Mac-Dev Enthusiasts. Welcome to the world of Mac productivity! In this blog, we'll explore a curated list of apps...",
      content: "Optimize Your Workflow with These Must-Have Tools for Mac-Dev Enthusiasts",
      guid: "https://medium.com/p/5eaec55334d1",
      categories: ["Mac", "Development", "Apps", "Productivity"],
      thumbnail: "https://cdn-images-1.medium.com/max/1024/1*si40fx1xPQZQa1qiFI2wIw.png",
      author: "HyperFox",
      readTime: "7 min read"
    },
    {
      title: "Git on Mac with Homebrew: Quick Setup Guide",
      link: "https://medium.com/@hyperfox_/git-on-mac-with-homebrew-quick-setup-guide-e8763fed34d3",
      pubDate: "2023-12-23T16:51:38.437Z",
      description: "Git'n'Sync on Your Mac: A Brew-tiful Setup Guide. Hey fellow code enthusiasts! Ever felt like your MacBook is missing that Git magic?...",
      content: "Git'n'Sync on Your Mac: A Brew-tiful Setup Guide",
      guid: "https://medium.com/p/e8763fed34d3",
      categories: ["Git", "Mac", "Homebrew", "Setup"],
      thumbnail: "https://cdn-images-1.medium.com/max/1024/1*TiVvfw7tF41RRnHhznItyg.png",
      author: "HyperFox",
      readTime: "5 min read"
    }
  ]
}

/**
 * Convert Medium blog posts to BlogPost format for unified display
 */
export function convertMediumToBlogPost(blogPost: MediumArticle): BlogPost {
  return {
    title: blogPost.title,
    slug: generateSlugFromUrl(blogPost.link),
    date: new Date(blogPost.pubDate).toISOString().split('T')[0],
    excerpt: blogPost.description,
    tags: blogPost.categories,
    content: blogPost.content,
    source: 'medium',
    externalUrl: blogPost.link,
    readTime: blogPost.readTime,
    author: blogPost.author,
    thumbnail: blogPost.thumbnail
  }
}

/**
 * Get all blog posts (local + Medium) sorted by date
 */
export async function getAllBlogPosts(): Promise<BlogPost[]> {
  try {
    const mediumBlogPosts = await fetchMediumBlogPosts()
    const mediumPosts = mediumBlogPosts.map(convertMediumToBlogPost)
    
    // TODO: Add local blog posts here when you have them
    const localPosts: BlogPost[] = []
    
    // Combine and sort by date
    const allPosts = [...localPosts, ...mediumPosts]
    return allPosts.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  } catch {
    return []
  }
}

// Helper functions
function extractImageFromContent(content: string): string {
  if (!content) return ''

  // Try to find images in various formats, prioritizing Medium CDN images
  const patterns = [
    // Medium CDN images (most common)
    /https:\/\/cdn-images-\d+-medium\.com\/[^\s<>"]+/i,
    /https:\/\/miro\.medium\.com\/[^\s<>"]+/i,
    // Standard img tags
    /<img[^>]+src="([^">]+)"/i,
    // Direct image URLs
    /https?:\/\/[^\s<>"]+\.(?:jpg|jpeg|png|gif|webp|avif)/i,
    // Any Medium image pattern
    /medium\.com\/[^\s<>"]*\.(?:jpg|jpeg|png|gif|webp)/i
  ]

  for (const pattern of patterns) {
    const match = content.match(pattern)
    if (match) {
      let imageUrl = match[1] || match[0]

      // Clean up the URL
      imageUrl = imageUrl.replace(/['"]/g, '').trim()

      // Ensure it's a valid URL
      if (imageUrl.startsWith('http')) {
        return imageUrl
      }
    }
  }

  return ''
}

function estimateReadTime(content: string): string {
  if (!content) return '1 min read'

  const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length
  const minutes = Math.max(1, Math.ceil(wordCount / 200))
  return `${minutes} min read`
}

function generateSlugFromUrl(url: string): string {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const slug = pathname.split('/').pop() || pathname.split('/').slice(-2, -1)[0]
    return slug || 'medium-blog-post'
  } catch {
    return 'medium-blog-post'
  }
}

/**
 * Check if a blog post is "new" (published within last 2 weeks)
 */
export function isBlogPostNew(dateString: string): boolean {
  try {
    const blogPostDate = new Date(dateString)
    const now = new Date()
    const twoWeeksAgo = new Date(now.getTime() - (14 * 24 * 60 * 60 * 1000))

    // Ensure valid date
    if (isNaN(blogPostDate.getTime())) {
      return false
    }

    return blogPostDate > twoWeeksAgo
  } catch {
    return false
  }
}

/**
 * Get days since blog post was published
 */
export function getDaysOld(dateString: string): number {
  try {
    const blogPostDate = new Date(dateString)
    const now = new Date()

    // Ensure valid date
    if (isNaN(blogPostDate.getTime())) {
      return 999 // Return a large number for invalid dates
    }

    const diffTime = now.getTime() - blogPostDate.getTime()
    const days = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    return Math.max(0, days) // Ensure non-negative
  } catch {
    return 999
  }
}

/**
 * Get a human-readable time difference
 */
export function getTimeAgo(dateString: string): string {
  try {
    const blogPostDate = new Date(dateString)
    const now = new Date()

    if (isNaN(blogPostDate.getTime())) {
      return 'Unknown date'
    }

    const diffTime = now.getTime() - blogPostDate.getTime()
    const days = Math.floor(diffTime / (1000 * 60 * 60 * 24))
    const hours = Math.floor(diffTime / (1000 * 60 * 60))
    const minutes = Math.floor(diffTime / (1000 * 60))

    if (days > 0) {
      return days === 1 ? '1 day ago' : `${days} days ago`
    } else if (hours > 0) {
      return hours === 1 ? '1 hour ago' : `${hours} hours ago`
    } else if (minutes > 0) {
      return minutes === 1 ? '1 minute ago' : `${minutes} minutes ago`
    } else {
      return 'Just now'
    }
  } catch {
    return 'Unknown date'
  }
}
