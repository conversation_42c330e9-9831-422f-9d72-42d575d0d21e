/**
 * API Response Utilities
 * 
 * Standardized response handlers for consistent API responses across all endpoints.
 * Provides success, error, and validation response patterns.
 */

import { NextResponse } from 'next/server';
import { ApiResponse, ApiError, ApiErrorCode } from './types';

/**
 * Create a standardized success response
 */
export function createSuccessResponse<T>(
  data: T,
  options: {
    message?: string;
    meta?: ApiResponse['meta'];
    status?: number;
  } = {}
): NextResponse<ApiResponse<T>> {
  const { message, meta, status = 200 } = options;

  const response: ApiResponse<T> = {
    success: true,
    data,
    timestamp: new Date().toISOString(),
    ...(message && { message }),
    ...(meta && { meta }),
  };

  return NextResponse.json(response, { status });
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
  error: ApiError | string,
  options: {
    details?: unknown;
    status?: number;
  } = {}
): NextResponse<ApiResponse> {
  const { details, status } = options;

  let errorResponse: ApiResponse;

  if (typeof error === 'string') {
    errorResponse = {
      success: false,
      error,
      code: ApiErrorCode.INTERNAL_ERROR,
      timestamp: new Date().toISOString(),
    };
    if (details) {
      errorResponse.details = details;
    }
  } else {
    errorResponse = {
      success: false,
      error: error.message,
      code: error.code,
      timestamp: new Date().toISOString(),
    };
    if (error.details) {
      errorResponse.details = error.details;
    }
    if (details) {
      errorResponse.details = details;
    }
  }

  const responseStatus = status || (typeof error === 'object' ? error.statusCode : 500);

  return NextResponse.json(errorResponse, { status: responseStatus });
}

/**
 * Create a validation error response
 */
export function createValidationErrorResponse(
  errors: string[],
  message: string = 'Validation failed'
): NextResponse<ApiResponse> {
  const response: ApiResponse = {
    success: false,
    error: message,
    errors,
    code: ApiErrorCode.VALIDATION_ERROR,
    timestamp: new Date().toISOString(),
  };

  return NextResponse.json(response, { status: 400 });
}

/**
 * Create a rate limit error response
 */
export function createRateLimitResponse(
  resetTime?: number
): NextResponse<ApiResponse> {
  const response: ApiResponse = {
    success: false,
    error: 'Too many requests. Please try again later.',
    code: ApiErrorCode.RATE_LIMITED,
    timestamp: new Date().toISOString(),
    ...(resetTime && { meta: { resetTime } }),
  };

  return NextResponse.json(response, { status: 429 });
}

/**
 * Create a not found error response
 */
export function createNotFoundResponse(
  resource: string = 'Resource'
): NextResponse<ApiResponse> {
  const response: ApiResponse = {
    success: false,
    error: `${resource} not found`,
    code: ApiErrorCode.NOT_FOUND,
    timestamp: new Date().toISOString(),
  };

  return NextResponse.json(response, { status: 404 });
}

/**
 * Create CORS headers for API responses
 */
export function createCorsHeaders(methods: string[] = ['GET', 'OPTIONS']): Record<string, string> {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': methods.join(', '),
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400',
  };
}

/**
 * Create a standardized OPTIONS response for CORS
 */
export function createOptionsResponse(methods: string[] = ['GET', 'OPTIONS']): NextResponse {
  return new NextResponse(null, {
    status: 200,
    headers: createCorsHeaders(methods),
  });
}
