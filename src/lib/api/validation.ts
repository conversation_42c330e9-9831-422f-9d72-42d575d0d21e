/**
 * API Validation Utilities
 * 
 * Centralized validation functions for API endpoints.
 * Provides consistent validation patterns and error messages.
 */

import { ValidationResult } from './types';

/**
 * Email validation regex
 */
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

/**
 * Validate email address
 */
export function validateEmail(email: string): boolean {
  return EMAIL_REGEX.test(email);
}

/**
 * Sanitize input string
 */
export function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

/**
 * Validate string length
 */
export function validateLength(
  value: string,
  min: number,
  max: number,
  fieldName: string
): string | null {
  if (value.length < min) {
    return `${fieldName} must be at least ${min} characters long`;
  }
  if (value.length > max) {
    return `${fieldName} must be less than ${max} characters`;
  }
  return null;
}

/**
 * Validate required field
 */
export function validateRequired(value: unknown, fieldName: string): string | null {
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return `${fieldName} is required`;
  }
  return null;
}

/**
 * Contact form validation
 */
export interface ContactFormData {
  name: string;
  email: string;
  message: string;
  subject?: string;
  company?: string;
}

export function validateContactForm(data: ContactFormData): ValidationResult {
  const errors: string[] = [];

  // Validate required fields
  const nameError = validateRequired(data.name, 'Name');
  if (nameError) errors.push(nameError);

  const emailError = validateRequired(data.email, 'Email');
  if (emailError) errors.push(emailError);

  const messageError = validateRequired(data.message, 'Message');
  if (messageError) errors.push(messageError);

  // Validate email format
  if (data.email && !validateEmail(data.email)) {
    errors.push('Please provide a valid email address');
  }

  // Validate lengths
  if (data.name) {
    const nameLengthError = validateLength(data.name, 2, 100, 'Name');
    if (nameLengthError) errors.push(nameLengthError);
  }

  if (data.message) {
    const messageLengthError = validateLength(data.message, 10, 2000, 'Message');
    if (messageLengthError) errors.push(messageLengthError);
  }

  if (data.subject) {
    const subjectLengthError = validateLength(data.subject, 1, 200, 'Subject');
    if (subjectLengthError) errors.push(subjectLengthError);
  }

  if (data.company) {
    const companyLengthError = validateLength(data.company, 1, 100, 'Company');
    if (companyLengthError) errors.push(companyLengthError);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Sanitize contact form data
 */
export function sanitizeContactForm(data: Record<string, unknown>): ContactFormData {
  return {
    name: sanitizeInput(String(data.name || '')),
    email: sanitizeInput(String(data.email || '')),
    message: sanitizeInput(String(data.message || '')),
    subject: data.subject ? sanitizeInput(String(data.subject)) : undefined,
    company: data.company ? sanitizeInput(String(data.company)) : undefined,
  };
}

/**
 * Validate pagination parameters
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
}

export function validatePagination(params: Record<string, unknown>): {
  page: number;
  limit: number;
  errors: string[];
} {
  const errors: string[] = [];
  let page = 1;
  let limit = 10;

  if (params.page !== undefined) {
    const pageNum = parseInt(String(params.page));
    if (isNaN(pageNum) || pageNum < 1) {
      errors.push('Page must be a positive integer');
    } else if (pageNum > 1000) {
      errors.push('Page number too large');
    } else {
      page = pageNum;
    }
  }

  if (params.limit !== undefined) {
    const limitNum = parseInt(String(params.limit));
    if (isNaN(limitNum) || limitNum < 1) {
      errors.push('Limit must be a positive integer');
    } else if (limitNum > 100) {
      errors.push('Limit cannot exceed 100');
    } else {
      limit = limitNum;
    }
  }

  return { page, limit, errors };
}
