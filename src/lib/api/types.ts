/**
 * API Types and Interfaces
 * 
 * Standardized types for API responses, errors, and common patterns.
 * Ensures consistency across all API endpoints.
 */

// Standard API Response Structure
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  errors?: string[];
  message?: string;
  code?: string;
  timestamp?: string;
  details?: unknown;
  meta?: {
    count?: number;
    page?: number;
    totalPages?: number;
    lastUpdated?: string;
    [key: string]: unknown;
  };
}

// Error Types
export enum ApiErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  RATE_LIMITED = 'RATE_LIMITED',
  NOT_FOUND = 'NOT_FOUND',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  CACHE_ERROR = 'CACHE_ERROR',
}

// Standard Error Response
export interface ApiError {
  code: ApiErrorCode;
  message: string;
  details?: unknown;
  statusCode: number;
}

// Request Context
export interface RequestContext {
  ip?: string;
  userAgent?: string;
  timestamp: number;
  requestId?: string;
}

// Validation Result
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

// Cache Entry
export interface CacheEntry<T = unknown> {
  data: T;
  timestamp: number;
  expiresAt: number;
  key: string;
}

// Rate Limit Info
export interface RateLimitInfo {
  count: number;
  resetTime: number;
  limit: number;
  remaining: number;
}
