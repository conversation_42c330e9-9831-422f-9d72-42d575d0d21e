/**
 * API Middleware Utilities
 * 
 * Reusable middleware functions for API routes including rate limiting,
 * request context extraction, and error handling.
 */

import { NextRequest } from 'next/server';
import { RequestContext, RateLimitInfo } from './types';

/**
 * In-memory rate limit storage
 * In production, use Redis or a database
 */
const rateLimitMap = new Map<string, RateLimitInfo>();

/**
 * Extract request context information
 */
export function getRequestContext(request: NextRequest): RequestContext {
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';
  const userAgent = request.headers.get('user-agent') || 'unknown';

  return {
    ip,
    userAgent,
    timestamp: Date.now(),
    requestId: generateRequestId(),
  };
}

/**
 * Generate a unique request ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Rate limiting configuration
 */
export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (request: NextRequest) => string;
}

/**
 * Default rate limit configurations
 */
export const RATE_LIMIT_CONFIGS = {
  strict: { windowMs: 60 * 1000, maxRequests: 5 }, // 5 requests per minute
  moderate: { windowMs: 60 * 1000, maxRequests: 30 }, // 30 requests per minute
  lenient: { windowMs: 60 * 1000, maxRequests: 100 }, // 100 requests per minute
} as const;

/**
 * Default key generator for rate limiting (uses IP address)
 */
function defaultKeyGenerator(request: NextRequest): string {
  const context = getRequestContext(request);
  return `rate_limit:${context.ip}`;
}

/**
 * Check if request is rate limited
 */
export function checkRateLimit(
  request: NextRequest,
  config: RateLimitConfig
): { isLimited: boolean; info: RateLimitInfo } {
  const keyGenerator = config.keyGenerator || defaultKeyGenerator;
  const key = keyGenerator(request);
  const now = Date.now();

  let limitInfo = rateLimitMap.get(key);

  // Initialize or reset if window expired
  if (!limitInfo || now > limitInfo.resetTime) {
    limitInfo = {
      count: 1,
      resetTime: now + config.windowMs,
      limit: config.maxRequests,
      remaining: config.maxRequests - 1,
    };
    rateLimitMap.set(key, limitInfo);
    return { isLimited: false, info: limitInfo };
  }

  // Check if limit exceeded
  if (limitInfo.count >= config.maxRequests) {
    return { isLimited: true, info: limitInfo };
  }

  // Increment count
  limitInfo.count++;
  limitInfo.remaining = Math.max(0, config.maxRequests - limitInfo.count);

  return { isLimited: false, info: limitInfo };
}

/**
 * Clean up expired rate limit entries
 */
export function cleanupRateLimits(): void {
  const now = Date.now();
  for (const [key, info] of rateLimitMap.entries()) {
    if (now > info.resetTime) {
      rateLimitMap.delete(key);
    }
  }
}

/**
 * Parse JSON body with error handling
 */
export async function parseJsonBody<T = unknown>(request: NextRequest): Promise<T | null> {
  try {
    const body = await request.json();
    return body as T;
  } catch (error) {
    console.error('Failed to parse JSON body:', error);
    return null;
  }
}

/**
 * Extract query parameters with type conversion
 */
export function getQueryParams(request: NextRequest): Record<string, string | string[]> {
  const { searchParams } = new URL(request.url);
  const params: Record<string, string | string[]> = {};

  for (const [key, value] of searchParams.entries()) {
    if (params[key]) {
      // Convert to array if multiple values
      if (Array.isArray(params[key])) {
        (params[key] as string[]).push(value);
      } else {
        params[key] = [params[key] as string, value];
      }
    } else {
      params[key] = value;
    }
  }

  return params;
}

/**
 * Log API request for debugging
 */
export function logRequest(
  request: NextRequest,
  context: RequestContext,
  additionalInfo?: Record<string, unknown>
): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[API] ${request.method} ${request.url}`, {
      requestId: context.requestId,
      ip: context.ip,
      userAgent: context.userAgent,
      timestamp: new Date(context.timestamp).toISOString(),
      ...additionalInfo,
    });
  }
}

/**
 * Setup periodic cleanup of rate limits
 */
if (typeof window === 'undefined') {
  // Only run on server side
  setInterval(cleanupRateLimits, 5 * 60 * 1000); // Clean up every 5 minutes
}
