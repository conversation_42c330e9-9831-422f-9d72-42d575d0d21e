export const blogConfig = {
  // Medium configuration
  medium: {
    // Your actual Medium username
    username: 'hyperfox_',
    // Alternative: Use your Medium publication name
    // publicationName: 'your-publication-name',

    // RSS feed URLs
    get userFeedUrl() {
      return `https://medium.com/feed/@${this.username}`
    },

    get publicationFeedUrl() {
      // Uncomment and modify if you want to use a publication feed
      // return `https://medium.com/feed/${this.publicationName}`
      return null
    },

    // Cache settings
    cacheDuration: 30 * 60 * 1000, // 30 minutes in milliseconds
  },
  
  // Blog settings
  settings: {
    postsPerPage: 12,
    showNewBadgeForDays: 14,
    autoRefreshInterval: 5 * 60 * 1000, // 5 minutes
  },
  
  // Sample data for development
  sampleData: {
    enabled: true,
    blogPosts: [
      {
        title: "Building AI-Powered Applications with Next.js",
        description: "Learn how to integrate AI capabilities into your Next.js applications using modern tools and techniques.",
        categories: ["AI", "Next.js", "Web Development"],
        daysOld: 2,
      },
      {
        title: "The Future of Generative AI in Software Development", 
        description: "Exploring how generative AI is transforming the way we write, test, and deploy software.",
        categories: ["AI", "Software Development", "Technology"],
        daysOld: 5,
      },
      {
        title: "Optimizing React Performance with Modern Techniques",
        description: "Advanced strategies for improving React application performance in 2024.",
        categories: ["React", "Performance", "JavaScript"],
        daysOld: 10,
      }
    ]
  }
}

// Instructions for setup:
// 1. Replace 'thanmaisai' with your actual Medium username
// 2. If you use a Medium publication, uncomment and set publicationName
// 3. Set useSampleData to false when you have real blog posts
// 4. Adjust cache duration and other settings as needed
