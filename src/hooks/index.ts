/**
 * Hooks Index
 *
 * Centralized exports for all custom React hooks.
 * Provides a clean API for importing hooks throughout the application.
 */

// Data Management Hooks
export { useBlogData, useLatestPost } from './useBlogData';
export { useNewBlogPosts } from './useNewBlogPosts';
export { usePagination } from './usePagination';
export { useTechStackData } from './useTechStackData';

// UI/UX Hooks
export { useCommandMenu } from './useCommandMenu';
export { useFocusManagement } from './useFocusManagement';
export { usePageLoader } from './usePageLoader';
export { useScrollBehavior } from './useScrollBehavior';

// Feature Hooks
export { useKeyboardShortcuts } from './useKeyboardShortcuts';
export { useVoiceAssistant } from './useVoiceAssistant';
