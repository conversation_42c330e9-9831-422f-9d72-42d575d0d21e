import { useEffect } from 'react';

/**
 * Hook to enhance accessibility by fixing missing attributes
 */
export function useFocusManagement() {
  useEffect(() => {
    // Fix elements missing proper accessibility attributes
    const fixAccessibilityAttributes = () => {
      // Fix icon buttons without accessible names
      document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])').forEach((button) => {
        if (!button.textContent?.trim() && button.querySelector('svg')) {
          const svgTitle = button.querySelector('svg title');
          button.setAttribute('aria-label',
            svgTitle?.textContent ||
            (button.closest('nav') ? 'Navigation button' : 'Button')
          );
        }
      });

      // Fix icon links without accessible names
      document.querySelectorAll('a:not([aria-label]):not([aria-labelledby])').forEach((link) => {
        if (!link.textContent?.trim() && link.querySelector('svg')) {
          const svgTitle = link.querySelector('svg title');
          link.setAttribute('aria-label',
            svgTitle?.textContent ||
            (link.closest('nav') ? 'Navigation link' : 'Link')
          );
        }
      });

      // Fix images without alt text
      document.querySelectorAll('img:not([alt])').forEach((img) => {
        img.setAttribute('alt', '');
      });

      // Fix form elements without labels
      document.querySelectorAll('input, select, textarea').forEach((element) => {
        const hasLabel = document.querySelector(`label[for="${element.id}"]`);
        if (!hasLabel && !element.hasAttribute('aria-label') && !element.hasAttribute('aria-labelledby')) {
          element.setAttribute('aria-label',
            element.getAttribute('placeholder') || 'Form field'
          );
        }
      });
    };

    // Run immediately
    fixAccessibilityAttributes();

    // Set up observer for DOM changes
    const observer = new MutationObserver((mutations) => {
      if (mutations.some(mutation => mutation.addedNodes.length > 0)) {
        fixAccessibilityAttributes();
      }
    });

    observer.observe(document.body, { childList: true, subtree: true });

    return () => observer.disconnect();
  }, []);
}
