import { useState, useEffect, useCallback } from 'react'
import { BlogPost, EnhancedBlogPost } from '@/types/content'
import { getDaysOld, isBlogPostNew } from '@/lib/api/medium'
import { blogConfig } from '@/config/blog'
import { getCachedBlogData, preloadBlogData } from '@/lib/preload'

interface UseBlogDataReturn {
  posts: EnhancedBlogPost[]
  loading: boolean
  error: string | null
  lastUpdated: string | null
  newPostsCount: number
  refreshPosts: () => Promise<void>
}

export function useBlogData(): UseBlogDataReturn {
  const [posts, setPosts] = useState<EnhancedBlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<string | null>(null)

  // Initialize with cached data if available
  useEffect(() => {
    const cachedData = getCachedBlogData()
    if (cachedData && cachedData.length > 0) {
      // Enhance cached posts with "new" status
      const enhancedPosts: EnhancedBlogPost[] = cachedData.map((post: EnhancedBlogPost) => ({
        ...post,
        isNew: isBlogPostNew(post.date),
        daysOld: getDaysOld(post.date)
      }))
      setPosts(enhancedPosts)
      setLoading(false) // No loading needed if we have cached data
    }
  }, [])

  const fetchPosts = useCallback(async (forceRefresh = false) => {
    try {
      // Only show loading if we don't have cached data or force refresh
      const cachedData = getCachedBlogData()
      if (!cachedData || forceRefresh) {
        setLoading(true)
      }
      setError(null)

      // Try to use preloaded data first
      const data = await preloadBlogData()

      if (data && data.length > 0) {
        // Enhance posts with "new" status
        const enhancedPosts: EnhancedBlogPost[] = data.map((post: EnhancedBlogPost) => ({
          ...post,
          isNew: isBlogPostNew(post.date),
          daysOld: getDaysOld(post.date)
        }))

        setPosts(enhancedPosts)
        setLastUpdated(new Date().toISOString())
      } else {
        throw new Error('No data received')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch posts')
    } finally {
      setLoading(false)
    }
  }, [])

  // Auto-refresh functionality
  useEffect(() => {
    // Only fetch if we don't have cached data
    const cachedData = getCachedBlogData()
    if (!cachedData || cachedData.length === 0) {
      fetchPosts()
    }

    // Set up auto-refresh interval if configured
    const autoRefreshInterval = blogConfig.settings.autoRefreshInterval
    if (autoRefreshInterval > 0) {
      const interval = setInterval(() => fetchPosts(true), autoRefreshInterval)
      return () => clearInterval(interval)
    }
  }, [fetchPosts])

  // Calculate new posts count
  const newPostsCount = posts.filter(post => post.isNew).length

  return {
    posts,
    loading,
    error,
    lastUpdated,
    newPostsCount,
    refreshPosts: () => fetchPosts(true) // Force refresh when manually triggered
  }
}

// Hook for getting the latest post (used in About page)
export function useLatestPost() {
  const [latestPost, setLatestPost] = useState<BlogPost | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchLatestPost = async () => {
      try {
        // Try cached data first
        const cachedData = getCachedBlogData()
        if (cachedData && cachedData.length > 0) {
          const latest = cachedData[0] // First post is the latest due to sorting
          if (isBlogPostNew(latest.date)) {
            setLatestPost(latest)
          }
          setLoading(false)
          return
        }

        // Fallback to preload function
        const data = await preloadBlogData()
        if (data && data.length > 0) {
          const latest = data[0] // First post is the latest due to sorting
          if (isBlogPostNew(latest.date)) {
            setLatestPost(latest)
          }
        }
      } catch {
        // Silently handle error
      } finally {
        setLoading(false)
      }
    }

    fetchLatestPost()
  }, [])

  return {
    latestPost,
    loading,
    isNew: latestPost ? isBlogPostNew(latestPost.date) : false,
    daysOld: latestPost ? getDaysOld(latestPost.date) : 0
  }
}
