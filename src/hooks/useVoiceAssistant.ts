"use client";

import { useState, useEffect, useRef } from 'react';
import Vapi from '@vapi-ai/web';

// Get Vapi public key from environment variables
// Make sure to use the PUBLIC key from your Vapi dashboard, not the private key
const VAPI_PUBLIC_KEY = process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY || "";

// Get assistant ID from environment variables
// You would get this from your Vapi dashboard after creating an assistant
const ASSISTANT_ID = process.env.ASSISTANT_ID || "";

// Custom assistant configuration as a fallback or alternative to using a pre-created assistant
// This allows you to create an assistant on-the-fly with your personalized information
const CUSTOM_ASSISTANT_CONFIG = {
  name: "<PERSON><PERSON><PERSON> Portfolio Assistant",
  transcriber: {
    provider: "deepgram" as const,
    model: "nova-2",
    language: "en-US" as const,
  },
  model: {
    provider: "openai" as const,
    model: "gpt-4" as const, // Using GPT-4 for better understanding and responses
    temperature: 0.7,
    systemPrompt: `You are <PERSON><PERSON><PERSON>, the creator of this portfolio website. Answer as if you are <PERSON><PERSON><PERSON> personally.

About me:
- I'm a developer with expertise in web development, React, Next.js, and UI/UX design
- My background includes work on various portfolio projects and professional experience
- I've created projects like this portfolio website with multiple themes and accessibility features
- I'm passionate about creating beautiful, functional, and accessible web experiences

When users ask questions:
- Be friendly, professional, and personable
- Provide specific details about my projects, skills, and experience
- If asked about contact information, direct them to the contact section
- Keep responses concise but informative

My portfolio showcases:
- My projects with detailed descriptions
- My experience and skills
- My blog posts on various tech topics
- My services and what I can offer

My key skills include:
- Frontend development with React and Next.js
- UI/UX design and implementation
- Accessibility-focused development
- Responsive web design
- JavaScript/TypeScript expertise

My notable projects include:
- This portfolio website with theme switching and voice assistant features
- Various other projects showcased in the Projects section`
  },
  voice: {
    provider: "playht" as const,
    voiceId: "jennifer", // Choose a voice that represents you
  },
  firstMessage: "Hello! I'm Thanmai's virtual assistant. How can I help you learn more about my portfolio and experience?"
};

// Flag to determine whether to use the pre-created assistant or the custom configuration
// Set to true to use the custom config instead of the assistant ID
// You can also set this based on environment variables if needed
const USE_CUSTOM_CONFIG = true;

export function useVoiceAssistant() {
  const [isOpen, setIsOpen] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [response, setResponse] = useState('');
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [volumeLevel, setVolumeLevel] = useState(0); // Track volume level
  const [isSpeaking, setIsSpeaking] = useState(false); // Track if assistant is speaking

  // Use a ref to store the Vapi instance
  const vapiRef = useRef<Vapi | null>(null);
  const callRef = useRef<unknown>(null);

  // Initialize Vapi
  useEffect(() => {
    try {
      // Create a new Vapi instance
      vapiRef.current = new Vapi(VAPI_PUBLIC_KEY);

      // Set up event listeners
      if (vapiRef.current) {
        // When the assistant starts speaking
        vapiRef.current.on("speech-start", () => {
          setIsListening(false);
          setIsSpeaking(true);
        });

        // When the assistant stops speaking
        vapiRef.current.on("speech-end", () => {
          // Ready for user input again
          setIsSpeaking(false);
        });

        // Track volume levels
        vapiRef.current.on("volume-level", (volume) => {
          setVolumeLevel(volume); // Volume is a value between 0 and 1
        });

        // When the call starts
        vapiRef.current.on("call-start", () => {
          setIsListening(true);
        });

        // When the call ends
        vapiRef.current.on("call-end", () => {
          setIsListening(false);
        });

        // Handle messages from the assistant
        vapiRef.current.on("message", (message) => {
          if (message.type === "transcript") {
            setTranscript(message.transcript?.text || '');
          } else if (message.type === "message") {
            setResponse(message.message?.content || '');
          }
        });

        // Handle errors
        vapiRef.current.on("error", () => {
          setError("An error occurred with the voice assistant.");
          setIsListening(false);
        });
      }

      setIsInitialized(true);
    } catch {
      setError("Failed to initialize voice assistant.");
    }

    // Cleanup function
    return () => {
      if (vapiRef.current) {
        try {
          vapiRef.current.stop();
        } catch {
          // Silent error handling for cleanup
        }
      }
    };
  }, []);

  // Toggle the assistant dialog
  const toggleAssistant = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      // Reset states when opening
      setTranscript('');
      setResponse('');
      setError(null);
    } else {
      // Stop the call when closing
      if (vapiRef.current && isListening) {
        try {
          vapiRef.current.stop();
        } catch {
          // Silent error handling for cleanup
        }
        setIsListening(false);
      }
    }
  };

  // Toggle mute/unmute
  const toggleMute = () => {
    if (!vapiRef.current) return;

    try {
      const newMutedState = !isMuted;
      vapiRef.current.setMuted(newMutedState);
      setIsMuted(newMutedState);

      if (newMutedState && isListening) {
        setIsListening(false);
      }
    } catch {
      setError("Error toggling microphone.");
    }
  };

  // Toggle listening state
  const toggleListening = () => {
    if (isMuted || !vapiRef.current || !isInitialized) return;

    try {
      if (isListening) {
        // Stop listening
        vapiRef.current.stop();
        setIsListening(false);
      } else {
        // Start listening
        setTranscript('');
        setResponse('');
        setError(null);

        // Start the call using either the assistant ID or custom configuration
        const assistantParam = USE_CUSTOM_CONFIG ? CUSTOM_ASSISTANT_CONFIG : ASSISTANT_ID;

        vapiRef.current.start(assistantParam)
          .then((call) => {
            callRef.current = call;
            setIsListening(true);
          })
          .catch(() => {
            setError("Failed to start voice assistant. Please check your API keys in the .env file and make sure they are correct.");

            // If using the assistant ID failed, try with the custom configuration as fallback
            if (!USE_CUSTOM_CONFIG) {
              vapiRef.current?.start(CUSTOM_ASSISTANT_CONFIG)
                .then((call) => {
                  callRef.current = call;
                  setIsListening(true);
                })
                .catch(() => {
                  setError("Both assistant methods failed. Please check your Vapi configuration.");
                });
            }
          });
      }
    } catch {
      setError("An error occurred with the voice assistant.");
    }
  };

  // Close the assistant
  const closeAssistant = () => {
    if (vapiRef.current && isListening) {
      try {
        vapiRef.current.stop();
      } catch {
        // Silent error handling for cleanup
      }
    }

    setIsOpen(false);
    setIsListening(false);
  };

  return {
    isOpen,
    isMuted,
    isListening,
    transcript,
    response,
    error,
    isInitialized,
    volumeLevel,
    isSpeaking,
    toggleAssistant,
    toggleMute,
    toggleListening,
    closeAssistant,
    setIsOpen,
  };
}
