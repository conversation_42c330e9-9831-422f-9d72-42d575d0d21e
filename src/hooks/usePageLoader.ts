import { useState, useEffect } from 'react';

export function usePageLoader() {
  const [isLoading, setIsLoading] = useState(true);
  const [hasVisited, setHasVisited] = useState(false);

  useEffect(() => {
    const hasVisitedBefore = sessionStorage.getItem('hyperfox-visited');
    
    if (hasVisitedBefore) {
      setIsLoading(false);
      setHasVisited(true);
    } else {
      setIsLoading(true);
      setHasVisited(false);
    }
  }, []);

  const handleLoadingComplete = () => {
    setIsLoading(false);
    setHasVisited(true);
    sessionStorage.setItem('hyperfox-visited', 'true');
  };

  return {
    isLoading: isLoading && !hasVisited,
    handleLoadingComplete,
  };
}
