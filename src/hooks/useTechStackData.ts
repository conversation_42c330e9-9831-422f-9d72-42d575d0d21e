import { useEffect, useState } from 'react';
import { TechCategory } from '@/types/techStack';
import { getCachedTechStackData, preloadTechStackData, isTechStackDataLoading } from '@/lib/airtable/preloadTechStack';

/**
 * Custom hook for fetching and caching tech stack data
 * Uses the preloaded data from preloadTechStack.ts when available
 */
export function useTechStackData() {
  const [techStackData, setTechStackData] = useState<TechCategory[]>(() => {
    // Try to use preloaded data on initial render
    return getCachedTechStackData() || [];
  });
  const [isLoading, setIsLoading] = useState<boolean>(() => {
    // If we already have cached data, we're not loading
    return !getCachedTechStackData() && isTechStackDataLoading();
  });
  const [error, setError] = useState<string | null>(null);

    useEffect(() => {
      const fetchData = async () => {
        try {
          // If we already have cached data, we're good to go
          const cachedData = getCachedTechStackData();
          if (cachedData && cachedData.length > 0) {
            setTechStackData(cachedData);
            setIsLoading(false);
            return;
          }
          
          // Otherwise, fetch fresh data using our preload function
          const data = await preloadTechStackData();
          
          // Update our local state with the data
          setTechStackData(data);
          setIsLoading(false);
        } catch (err) {
          console.error('Error fetching tech stack data:', err);
          setError(err instanceof Error ? err.message : 'An unknown error occurred');
          setIsLoading(false);
        }
      };

      // Only fetch if we don't already have the data
      if (techStackData.length === 0) {
        fetchData();
      } else {
        setIsLoading(false);
      }
    }, [techStackData.length]);

    return { techStackData, isLoading, error };
}
