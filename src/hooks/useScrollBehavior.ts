import { useEffect } from 'react';

/**
 * Hook to handle scroll restoration and prevent scroll-related issues
 */
export function useScrollBehavior() {
  useEffect(() => {
    // Ensure body can scroll
    const ensureScrollable = () => {
      document.body.style.overflowY = 'auto';
      document.documentElement.style.overflowY = 'auto';
      
      // Force reflow to ensure scroll behavior is applied
      void document.body.offsetHeight;
    };

    // Run immediately
    ensureScrollable();

    // Run after a short delay to handle any layout shifts
    const timeoutId = setTimeout(ensureScrollable, 100);

    // Handle route changes (for Next.js navigation)
    const handleRouteChange = () => {
      // Small delay to allow new content to render
      setTimeout(ensureScrollable, 50);
    };

    // Listen for navigation events
    window.addEventListener('popstate', handleRouteChange);
    
    // Listen for theme changes that might affect layout
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'class' || mutation.attributeName === 'style')) {
          ensureScrollable();
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class', 'style']
    });

    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['class', 'style']
    });

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('popstate', handleRouteChange);
      observer.disconnect();
    };
  }, []);
}
