import { useState, useEffect, useCallback } from 'react'
import { EnhancedBlogPost } from '@/types/content'
import { getCachedBlogData, preloadBlogData } from '@/lib/preload'

interface UseNewBlogPostsReturn {
  newBlogPosts: EnhancedBlogPost[]
  loading: boolean
  error: string | null
  dismissBlogPost: (slug: string) => void
  refreshBlogPosts: () => Promise<void>
  hasNewBlogPosts: boolean
}

// Using global preload cache for ultra-fast loading

export function useNewBlogPosts(): UseNewBlogPostsReturn {
  const [newBlogPosts, setNewBlogPosts] = useState<EnhancedBlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dismissedBlogPosts, setDismissedBlogPosts] = useState<Set<string>>(new Set())

  // Load dismissed blog posts from localStorage AND check for instant cache
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const dismissed = localStorage.getItem('dismissedBlogPosts')
      if (dismissed) {
        try {
          setDismissedBlogPosts(new Set(JSON.parse(dismissed)))
        } catch {
          // Invalid JSON, reset
          localStorage.removeItem('dismissedBlogPosts')
        }
      }

      // INSTANT LOADING: Check if we have cached data immediately
      const cachedData = getCachedBlogData()
      if (cachedData && cachedData.length > 0) {
        const twoWeeksAgo = new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)
        const dismissedSet = dismissed ? new Set(JSON.parse(dismissed)) : new Set()

        const recentBlogPosts = cachedData
          .filter((blogPost: EnhancedBlogPost) => {
            const blogPostDate = new Date(blogPost.date)
            const isRecent = blogPostDate > twoWeeksAgo
            const notDismissed = !dismissedSet.has(blogPost.slug)
            return isRecent && notDismissed
          })
          .slice(0, 3)

        setNewBlogPosts(recentBlogPosts)
        setLoading(false) // Instant loading complete!
      }
    }
  }, [])

  const fetchNewBlogPosts = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // ULTRA FAST: Use preloaded data first
      const cachedData = getCachedBlogData()
      if (cachedData && cachedData.length > 0) {
        const twoWeeksAgo = new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)
        const recentBlogPosts = cachedData
          .filter((blogPost: EnhancedBlogPost) => {
            const blogPostDate = new Date(blogPost.date)
            const isRecent = blogPostDate > twoWeeksAgo
            const notDismissed = !dismissedBlogPosts.has(blogPost.slug)
            return isRecent && notDismissed
          })
          .slice(0, 3)

        setNewBlogPosts(recentBlogPosts)
        setLoading(false)
        return
      }

      // Use preload system for ultra-fast loading
      const freshData = await preloadBlogData()

      if (freshData && freshData.length > 0) {
        // Filter for blog posts that are new (within 2 weeks) and not dismissed
        const twoWeeksAgo = new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)

        const recentBlogPosts = freshData
          .filter((blogPost: EnhancedBlogPost) => {
            const blogPostDate = new Date(blogPost.date)
            const isRecent = blogPostDate > twoWeeksAgo
            const notDismissed = !dismissedBlogPosts.has(blogPost.slug)
            return isRecent && notDismissed
          })
          .slice(0, 3) // Show max 3 new blog posts on homepage

        setNewBlogPosts(recentBlogPosts)
      } else {
        setError('No blog posts found')
      }
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        setError('Request timeout - please check your connection')
      } else {
        setError(err instanceof Error ? err.message : 'Failed to fetch blog posts')
      }
    } finally {
      setLoading(false)
    }
  }, [dismissedBlogPosts])

  const dismissBlogPost = useCallback((slug: string) => {
    const newDismissed = new Set(dismissedBlogPosts)
    newDismissed.add(slug)
    setDismissedBlogPosts(newDismissed)
    
    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('dismissedBlogPosts', JSON.stringify(Array.from(newDismissed)))
    }
    
    // Remove from current blog posts
    setNewBlogPosts(prev => prev.filter(blogPost => blogPost.slug !== slug))
  }, [dismissedBlogPosts])

  const refreshBlogPosts = useCallback(async () => {
    await fetchNewBlogPosts()
  }, [fetchNewBlogPosts])

  // Initial fetch
  useEffect(() => {
    fetchNewBlogPosts()
  }, [fetchNewBlogPosts])

  // Auto-refresh every 10 minutes to catch new blog posts (less aggressive)
  useEffect(() => {
    const interval = setInterval(() => {
      fetchNewBlogPosts()
    }, 10 * 60 * 1000) // 10 minutes

    return () => clearInterval(interval)
  }, [fetchNewBlogPosts])

  return {
    newBlogPosts,
    loading,
    error,
    dismissBlogPost,
    refreshBlogPosts,
    hasNewBlogPosts: newBlogPosts.length > 0
  }
}

// Utility function to check if a blog post is new (within 2 weeks)
export function isBlogPostNew(dateString: string): boolean {
  try {
    const blogPostDate = new Date(dateString)
    const twoWeeksAgo = new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)
    return blogPostDate > twoWeeksAgo
  } catch {
    return false
  }
}

// Utility function to get days since blog post was published
export function getDaysOld(dateString: string): number {
  try {
    const blogPostDate = new Date(dateString)
    const now = new Date()
    const diffTime = now.getTime() - blogPostDate.getTime()
    const days = Math.floor(diffTime / (1000 * 60 * 60 * 24))
    return Math.max(0, days)
  } catch {
    return 999
  }
}

// Utility function to format relative time
export function formatRelativeTime(dateString: string): string {
  try {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60))
    const diffMinutes = Math.floor(diffTime / (1000 * 60))

    if (diffMinutes < 60) {
      return diffMinutes <= 1 ? 'Just now' : `${diffMinutes} minutes ago`
    } else if (diffHours < 24) {
      return diffHours === 1 ? '1 hour ago' : `${diffHours} hours ago`
    } else if (diffDays === 0) {
      return 'Today'
    } else if (diffDays === 1) {
      return 'Yesterday'
    } else if (diffDays < 7) {
      return `${diffDays} days ago`
    } else if (diffDays < 14) {
      const weeks = Math.floor(diffDays / 7)
      return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      })
    }
  } catch {
    return 'Unknown date'
  }
}
