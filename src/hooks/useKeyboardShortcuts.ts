import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';

type KeyboardShortcutsProps = {
  setCommandOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setShortcutsHelpOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  isExtrasUnlocked?: boolean;
};

export function useKeyboardShortcuts({
  setCommandOpen,
  setShortcutsHelpOpen,
  isExtrasUnlocked = false,
}: KeyboardShortcutsProps) {
  const router = useRouter();
  const { setTheme } = useTheme();

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in input fields
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        e.target instanceof HTMLSelectElement
      ) {
        return;
      }

      // Command menu shortcut (Cmd+K or Ctrl+K)
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setCommandOpen(open => !open);
        return;
      }

      // Question mark to toggle shortcuts help
      if (e.key === '?' && !e.metaKey && !e.ctrlKey && !e.altKey) {
        e.preventDefault();
        setShortcutsHelpOpen?.(open => !open);
        return;
      }

      // ESC key to close modals
      if (e.key === 'Escape') {
        e.preventDefault();
        setCommandOpen(false);
        setShortcutsHelpOpen?.(false);
        return;
      }

      // Number keys for navigation (without modifiers)
      if (!e.metaKey && !e.ctrlKey && !e.altKey && !e.shiftKey) {
        switch (e.key) {
          case '1':
            e.preventDefault();
            router.push('/');
            break;
          case '2':
            e.preventDefault();
            router.push('/experience');
            break;
          case '3':
            e.preventDefault();
            router.push('/projects');
            break;
          // case '4': // Services temporarily commented out
          //   e.preventDefault();
          //   router.push('/services');
          //   break;
          case '4':
            e.preventDefault();
            router.push('/resume');
            break;
          case '5':
            e.preventDefault();
            router.push('/feed');
            break;
          case '6':
            e.preventDefault();
            router.push('/blog');
            break;
          case '7':
            e.preventDefault();
            router.push('/stack');
            break;
            router.push('/blog');
            break;
          case '8':
            e.preventDefault();
            router.push('/stack');
            break;
        }
      }

      // Special shortcuts with Ctrl
      if (e.ctrlKey && !e.metaKey && !e.altKey && !e.shiftKey) {
        switch (e.key) {
          case 'e': // Extras - Easter egg (only works when unlocked)
            if (isExtrasUnlocked) {
              e.preventDefault();
              router.push('/extras');
            }
            break;
          case 'c':
            e.preventDefault();
            router.push('/contact');
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [router, setTheme, setCommandOpen, setShortcutsHelpOpen, isExtrasUnlocked]);
}
