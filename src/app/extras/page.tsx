'use client';

import { motion, useAnimation } from 'framer-motion';
import React, { lazy, Suspense, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import './animations.css';

// Lazy load icons for better initial bundle size
const Camera = lazy(() => import('lucide-react').then(mod => ({ default: mod.Camera })));
const Film = lazy(() => import('lucide-react').then(mod => ({ default: mod.Film })));
const Heart = lazy(() => import('lucide-react').then(mod => ({ default: mod.Heart })));
const Languages = lazy(() => import('lucide-react').then(mod => ({ default: mod.Languages })));
const Lightbulb = lazy(() => import('lucide-react').then(mod => ({ default: mod.Lightbulb })));
const Minimize2 = lazy(() => import('lucide-react').then(mod => ({ default: mod.Minimize2 })));
const Palette = lazy(() => import('lucide-react').then(mod => ({ default: mod.Palette })));
const Plane = lazy(() => import('lucide-react').then(mod => ({ default: mod.Plane })));
const Smartphone = lazy(() => import('lucide-react').then(mod => ({ default: mod.Smartphone })));
const Target = lazy(() => import('lucide-react').then(mod => ({ default: mod.Target })));
const UtensilsCrossed = lazy(() =>
  import('lucide-react').then(mod => ({ default: mod.UtensilsCrossed }))
);

// Icon fallback component for better UX during loading
const IconFallback = ({ className }: { className?: string }) => (
  <div className={`${className} bg-neutral-600/20 rounded animate-pulse`} />
);

// Optimized hooks with better performance characteristics
const useReducedMotion = () => {
  const [shouldReduceMotion, setShouldReduceMotion] = useState(() => {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  });

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const handler = (e: MediaQueryListEvent) => setShouldReduceMotion(e.matches);

    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, []);

  return shouldReduceMotion;
};

// Enhanced intersection observer with root margin for better performance
const useInView = (threshold = 0.1, rootMargin = '50px') => {
  const [inView, setInView] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => setInView(entry.isIntersecting), {
      threshold,
      rootMargin,
    });

    const currentRef = ref.current;
    if (currentRef) observer.observe(currentRef);

    return () => {
      if (currentRef) observer.unobserve(currentRef);
    };
  }, [threshold, rootMargin]);

  return [ref, inView] as const;
};

// Custom hook for window size tracking
const useWindowSize = () => {
  const [windowSize, setWindowSize] = useState({ width: 1000, height: 1000 });

  useEffect(() => {
    const updateWindowSize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    updateWindowSize();

    let timeoutId: NodeJS.Timeout;
    const debouncedResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateWindowSize, 250);
    };

    window.addEventListener('resize', debouncedResize);

    return () => {
      window.removeEventListener('resize', debouncedResize);
      clearTimeout(timeoutId);
    };
  }, []);

  return windowSize;
};

// Optimized animation variants with better performance
const ANIMATION_VARIANTS = {
  fadeInUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.4, ease: [0.23, 1, 0.32, 1] },
  },
  staggerContainer: {
    animate: {
      transition: { staggerChildren: 0.06, delayChildren: 0.1 },
    },
  },
  scaleIn: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.3, ease: 'easeOut' },
  },
} as const;

// Memoized animation config to prevent recreations
const SPRING_CONFIG = { type: 'spring', stiffness: 300, damping: 30, mass: 0.1 } as const;
const HOVER_SCALE = { scale: 1.02 } as const;
const TAP_SCALE = { scale: 0.98 } as const;

// --- Optimized Pixel Art Runner Component ---
const PixelRunner = React.memo(() => {
  const [currentFrame, setCurrentFrame] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const controls = useAnimation();
  const shouldReduceMotion = useReducedMotion();

  // Memoized pixel frames - prevents recreation on every render
  const runFrames = useMemo(
    () => [
      [
        '⬛⬛⬛🟫🟫⬛⬛⬛',
        '⬛⬛🟫🟫🟫🟫⬛⬛',
        '⬛⬛🟫⬜⬜🟫⬛⬛',
        '⬛⬛🟫🟫🟫🟫⬛⬛',
        '⬛⬛🟦🟦🟦🟦⬛⬛',
        '⬛⬛🟦⬛🟦⬛⬛⬛',
        '⬛⬛🟦🟦🟦⬛⬛⬛',
        '⬛⬛⬛🟫⬛⬛⬛⬛',
      ],
      [
        '⬛⬛⬛🟫🟫⬛⬛⬛',
        '⬛⬛🟫🟫🟫🟫⬛⬛',
        '⬛⬛🟫⬜⬜🟫⬛⬛',
        '⬛⬛🟫🟫🟫🟫⬛⬛',
        '⬛⬛🟦🟦🟦🟦⬛⬛',
        '⬛⬛⬛🟦⬛🟦⬛⬛',
        '⬛⬛⬛🟦🟦🟦⬛⬛',
        '⬛⬛⬛⬛🟫⬛⬛⬛',
      ],
    ],
    []
  );

  // Optimized pixel color mapping with memoization
  const pixelClassMap = useMemo(
    () => ({
      '🟫': 'bg-orange-400',
      '🟦': 'bg-blue-500',
      '⬜': 'bg-white',
    }),
    []
  );

  const getPixelClass = useCallback(
    (pixel: string) => {
      return pixelClassMap[pixel as keyof typeof pixelClassMap] || 'bg-transparent';
    },
    [pixelClassMap]
  );

  useEffect(() => {
    // Only start animation when component becomes visible
    const observer = new IntersectionObserver(([entry]) => setIsVisible(entry.isIntersecting), {
      threshold: 0.1,
    });

    const element = document.querySelector('.pixel-runner-container');
    if (element) observer.observe(element);

    return () => {
      if (element) observer.unobserve(element);
    };
  }, []);

  useEffect(() => {
    if (!isVisible || shouldReduceMotion) return;

    const interval = setInterval(() => {
      setCurrentFrame(prev => (prev + 1) % runFrames.length);
    }, 300);

    // Optimized animation with will-change and transform3d
    const timer = setTimeout(() => {
      controls.start({
        x: [0, window.innerWidth + 100],
        transition: {
          duration: 12, // Slightly faster
          repeat: Infinity,
          ease: 'linear',
        },
      });
    }, 100);

    return () => {
      clearInterval(interval);
      clearTimeout(timer);
    };
  }, [controls, runFrames.length, isVisible, shouldReduceMotion]);

  if (shouldReduceMotion) {
    return (
      <div className="absolute bottom-4 left-4 z-20 scale-150 pixel-runner-container">
        <div className="transform scale-75">
          <div className="text-2xl">🏃‍♂️</div>
        </div>
      </div>
    );
  }

  const currentFrameData = runFrames[currentFrame];

  return (
    <motion.div
      animate={controls}
      className="absolute bottom-4 left-0 z-20 scale-150 gpu-accelerated pixel-runner-container"
      style={{
        imageRendering: 'pixelated',
        willChange: 'transform',
      }}
      initial={{ x: -100 }}
    >
      <div className="transform scale-75">
        {currentFrameData.map((row, i) => (
          <div key={i} className="flex">
            {row.split('').map((pixel, j) => (
              <div key={`${i}-${j}`} className={`w-1 h-1 ${getPixelClass(pixel)}`} />
            ))}
          </div>
        ))}
      </div>
    </motion.div>
  );
});

PixelRunner.displayName = 'PixelRunner';

// --- Optimized Aurora Background Component ---
const AuroraBackground = React.memo(() => (
  <div className="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
    {/* Optimized structural element only */}
  </div>
));

AuroraBackground.displayName = 'AuroraBackground';

// --- Optimized Spotlight Card Component ---
const SpotlightCard = React.memo(
  ({
    icon: Icon,
    color,
    title,
    description,
  }: {
    icon: React.ElementType;
    color: string;
    title: string;
    description: string;
  }) => {
    const shouldReduceMotion = useReducedMotion();

    return (
      <motion.div
        className="relative p-6 border border-neutral-800/80 rounded-2xl bg-neutral-900/60 backdrop-blur-sm gpu-accelerated"
        variants={ANIMATION_VARIANTS.scaleIn}
        whileHover={shouldReduceMotion ? undefined : HOVER_SCALE}
        transition={SPRING_CONFIG}
      >
        <div className="relative z-10 flex flex-col items-start gap-3">
          <Suspense fallback={<IconFallback className={`h-9 w-9`} />}>
            <Icon className={`h-9 w-9 ${color}`} />
          </Suspense>
          <div>
            <h3 className="font-bold text-lg text-neutral-50">{title}</h3>
            <p className="text-sm text-neutral-400 mt-1">{description}</p>
          </div>
        </div>
      </motion.div>
    );
  }
);

SpotlightCard.displayName = 'SpotlightCard';

// --- Optimized Magnetic Element ---
const Magnetic = React.memo(({ children }: { children: React.ReactNode }) => {
  const ref = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const shouldReduceMotion = useReducedMotion();
  const lastUpdate = useRef(0);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!ref.current || shouldReduceMotion) return;

      // Throttle to ~60fps for better performance
      const now = Date.now();
      if (now - lastUpdate.current < 16) return;
      lastUpdate.current = now;

      const { clientX, clientY } = e;
      const { width, height, left, top } = ref.current.getBoundingClientRect();
      const x = clientX - (left + width / 2);
      const y = clientY - (top + height / 2);
      setPosition({ x: x * 0.05, y: y * 0.05 });
    },
    [shouldReduceMotion]
  );

  const mouseLeave = useCallback(() => {
    setPosition({ x: 0, y: 0 });
  }, []);

  useEffect(() => {
    const currentRef = ref.current;
    if (currentRef && !shouldReduceMotion) {
      currentRef.addEventListener('mousemove', handleMouseMove);
      currentRef.addEventListener('mouseleave', mouseLeave);
    }
    return () => {
      if (currentRef) {
        currentRef.removeEventListener('mousemove', handleMouseMove);
        currentRef.removeEventListener('mouseleave', mouseLeave);
      }
    };
  }, [handleMouseMove, mouseLeave, shouldReduceMotion]);

  return (
    <motion.div
      ref={ref}
      animate={shouldReduceMotion ? {} : position}
      transition={SPRING_CONFIG}
      className="gpu-accelerated"
    >
      {children}
    </motion.div>
  );
});

Magnetic.displayName = 'Magnetic';

// --- Highly Optimized Floating Particles Component ---
const FloatingParticles = React.memo(() => {
  const shouldReduceMotion = useReducedMotion();
  const [containerRef, inView] = useInView(0.1, '100px');
  const windowSize = useWindowSize();

  // Memoized particles with better distribution and more particles
  const particles = useMemo(() => {
    if (shouldReduceMotion) return [];

    return Array.from({ length: 60 }, (_, i) => ({
      id: i,
      x: Math.random() * windowSize.width,
      y: Math.random() * windowSize.height, // Start throughout viewport
      delay: Math.random() * 6,
      duration: Math.random() * 8 + 6,
      size: Math.random() * 3 + 1,
      opacity: Math.random() * 0.6 + 0.2,
    }));
  }, [shouldReduceMotion, windowSize]);

  if (shouldReduceMotion || !inView) {
    return (
      <div
        ref={containerRef}
        className="absolute inset-0 overflow-hidden pointer-events-none"
        aria-hidden="true"
      />
    );
  }

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 overflow-hidden pointer-events-none"
      aria-hidden="true"
    >
      {particles.map(particle => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full gpu-accelerated"
          style={{
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: `rgba(255, 255, 255, ${particle.opacity})`,
            boxShadow: `0 0 ${particle.size * 2}px rgba(255, 255, 255, ${particle.opacity * 0.5})`,
            willChange: 'transform, opacity',
          }}
          initial={{
            x: particle.x,
            y: particle.y,
            opacity: 0,
          }}
          animate={{
            y: [particle.y, particle.y - 300, particle.y - 600],
            x: [particle.x, particle.x + (Math.random() - 0.5) * 100],
            opacity: [0, particle.opacity, particle.opacity, 0],
          }}
          transition={{
            duration: particle.duration,
            repeat: Infinity,
            delay: particle.delay,
            ease: 'linear',
          }}
        />
      ))}
    </div>
  );
});

FloatingParticles.displayName = 'FloatingParticles';

// --- Page ---

// --- Static Data (moved outside component to prevent recreations) ---
const FAVORITE_ARTISTS = ['Iqlipse Nova', 'Anvu Jain', 'Ed Sheeran', 'One Direction', 'Avicii'];

const PERSONAL_GOALS = [
  {
    id: 'saas',
    goal: 'Build a SaaS Product',
    description: 'Creating software that solves real problems.',
    icon: '🚀',
    color: 'border-orange-400',
  },
  {
    id: 'system-design',
    goal: 'Master System Design',
    description: 'Understanding scalable architecture.',
    icon: '🏗️',
    color: 'border-orange-400',
  },
  {
    id: 'health',
    goal: 'Salubrity',
    description: 'Optimal physical & mental well-being.',
    icon: '💪',
    color: 'border-emerald-400',
  },
  {
    id: 'eudaimonia',
    goal: 'Eudaimonia',
    description: 'A meaningful & flourishing life.',
    icon: '🌟',
    color: 'border-emerald-400',
  },
] as const;

const HOBBIES = [
  {
    id: 'photography',
    name: 'Photography',
    icon: Camera,
    color: 'text-purple-400',
    description: 'Capturing moments, telling stories.',
  },
  {
    id: 'video-editing',
    name: 'Video Editing',
    icon: Film,
    color: 'text-blue-400',
    description: 'Bringing narratives to life.',
  },
  {
    id: 'athletics',
    name: 'Athletics',
    icon: Target,
    color: 'text-green-400',
    description: 'Staying active and competitive.',
  },
  {
    id: 'sketching',
    name: 'Sketching',
    icon: Palette,
    color: 'text-orange-400',
    description: 'Expressing creativity on paper.',
  },
  {
    id: 'travelling',
    name: 'Travelling',
    icon: Plane,
    color: 'text-cyan-400',
    description: 'Exploring new cultures.',
  },
  {
    id: 'exploring',
    name: 'Exploring',
    icon: Lightbulb,
    color: 'text-yellow-400',
    description: 'Seeking new adventures.',
  },
] as const;

const FUN_FACTS = [
  {
    id: 'multilingual',
    title: 'Multilingual',
    icon: Languages,
    description: 'I speak 6 languages, bridging communication across cultures.',
    color: 'text-blue-400',
  },
  {
    id: 'minimalist',
    title: 'Minimalist',
    icon: Minimize2,
    description: 'Less is more. I focus on intentional living and what truly matters.',
    color: 'text-green-400',
  },
  {
    id: 'technophile',
    title: 'Technophile',
    icon: Smartphone,
    description: "Fascinated by how tech shapes our world. Always excited for what's next.",
    color: 'text-cyan-400',
  },
  {
    id: 'foodie',
    title: 'Foodie',
    icon: UtensilsCrossed,
    description: 'Good food fuels good code! I love exploring different cuisines.',
    color: 'text-red-400',
  },
] as const;

export default function ExtrasPage() {
  return (
    <div className="relative min-h-screen w-full text-neutral-50 py-24 sm:py-32 overflow-hidden">
      <AuroraBackground />
      <FloatingParticles />

      <div className="container mx-auto px-4 space-y-24 sm:space-y-32 relative z-10">
        <motion.header
          className="text-center space-y-4"
          variants={ANIMATION_VARIANTS.fadeInUp}
          initial="initial"
          animate="animate"
        >
          <motion.h1
            className="text-5xl sm:text-6xl lg:text-7xl font-bold tracking-tighter text-neutral-50"
            variants={ANIMATION_VARIANTS.scaleIn}
          >
            The Person Behind the Code
          </motion.h1>
          <motion.p
            className="text-lg sm:text-xl text-neutral-400 max-w-2xl mx-auto"
            variants={ANIMATION_VARIANTS.fadeInUp}
          >
            A glimpse into the interests, aspirations, and quirks that fuel my passion and
            creativity.
          </motion.p>
        </motion.header>

        {/* --- Music Section --- */}
        <motion.section
          className="space-y-8"
          variants={ANIMATION_VARIANTS.fadeInUp}
          initial="initial"
          animate="animate"
        >
          <motion.h2
            className="text-3xl sm:text-4xl font-bold tracking-tight text-center text-neutral-50"
            variants={ANIMATION_VARIANTS.scaleIn}
          >
            My Soundtrack
          </motion.h2>
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
            <motion.div
              className="lg:col-span-3 rounded-2xl border border-neutral-800/80 bg-neutral-900/60 overflow-hidden p-1.5 h-full backdrop-blur-sm shadow-2xl"
              variants={ANIMATION_VARIANTS.scaleIn}
              whileHover={HOVER_SCALE}
              transition={SPRING_CONFIG}
            >
              <iframe
                className="rounded-xl w-full h-[480px]"
                src="https://open.spotify.com/embed/playlist/3gOFOSCRqgrWTvAoU5HO9j?utm_source=generator&theme=0"
                frameBorder="0"
                allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture"
                loading="lazy"
                title="Spotify Playlist"
              />
            </motion.div>
            <motion.div
              className="lg:col-span-2 rounded-2xl border border-neutral-800/80 bg-neutral-900/60 p-6 flex flex-col h-full backdrop-blur-sm shadow-xl"
              variants={ANIMATION_VARIANTS.scaleIn}
            >
              <h3 className="font-bold text-xl mb-4 text-neutral-50">Artists on Repeat</h3>
              <div className="flex flex-wrap gap-2">
                {FAVORITE_ARTISTS.map(artist => (
                  <span
                    key={artist}
                    className="rounded-full bg-neutral-800/50 border border-neutral-700/50 px-3.5 py-1.5 text-sm font-medium text-neutral-200 backdrop-blur-sm"
                  >
                    {artist}
                  </span>
                ))}
              </div>
              <p className="text-xs text-neutral-500 italic mt-auto pt-4">
                The soundtrack to my creative process and daily life. ✨
              </p>
            </motion.div>
          </div>
        </motion.section>

        {/* --- Goals & Hobbies Grid --- */}
        <motion.section
          className="grid grid-cols-1 lg:grid-cols-2 gap-12"
          variants={ANIMATION_VARIANTS.staggerContainer}
          initial="initial"
          animate="animate"
        >
          <motion.div variants={ANIMATION_VARIANTS.fadeInUp}>
            <motion.h2 className="text-3xl sm:text-4xl font-bold tracking-tight mb-8 flex items-center gap-3 text-neutral-50">
              <Suspense fallback={<IconFallback className="h-8 w-8" />}>
                <Target className="text-emerald-400" />
              </Suspense>
              Current Aspirations
            </motion.h2>
            <motion.div className="space-y-4" variants={ANIMATION_VARIANTS.staggerContainer}>
              {PERSONAL_GOALS.map(goal => (
                <motion.div
                  key={goal.id}
                  className="relative flex items-center gap-4 border-b border-neutral-800 pb-4 rounded-lg p-4 backdrop-blur-sm"
                  variants={ANIMATION_VARIANTS.scaleIn}
                  whileHover={{ x: 5 }}
                  transition={SPRING_CONFIG}
                >
                  <span className="text-4xl">{goal.icon}</span>
                  <div>
                    <h3 className="font-semibold text-lg text-neutral-50">{goal.goal}</h3>
                    <p className="text-neutral-400">{goal.description}</p>
                  </div>
                  <div className="absolute bottom-0 left-0 h-0.5 w-full bg-neutral-700" />
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          <motion.div variants={ANIMATION_VARIANTS.fadeInUp}>
            <motion.h2 className="text-3xl sm:text-4xl font-bold tracking-tight mb-8 flex items-center gap-3 text-neutral-50">
              <Suspense fallback={<IconFallback className="h-8 w-8" />}>
                <Heart className="text-pink-400" />
              </Suspense>
              Beyond the Screen
            </motion.h2>
            <motion.div
              className="grid grid-cols-2 sm:grid-cols-3 gap-4"
              variants={ANIMATION_VARIANTS.staggerContainer}
            >
              {HOBBIES.map(hobby => (
                <motion.div
                  key={hobby.id}
                  className="flex flex-col items-center justify-center p-4 border border-neutral-800/50 rounded-xl bg-neutral-900/30 backdrop-blur-sm"
                  variants={ANIMATION_VARIANTS.scaleIn}
                  whileHover={{ y: -2 }}
                  transition={SPRING_CONFIG}
                >
                  <Suspense fallback={<IconFallback className={`h-10 w-10`} />}>
                    <hobby.icon className={`h-10 w-10 ${hobby.color}`} />
                  </Suspense>
                  <p className="mt-2 text-sm text-center text-neutral-200">{hobby.name}</p>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </motion.section>

        {/* --- Fun Facts --- */}
        <motion.section
          className="space-y-8"
          variants={ANIMATION_VARIANTS.fadeInUp}
          initial="initial"
          animate="animate"
        >
          <motion.h2 className="text-3xl sm:text-4xl font-bold tracking-tight text-center text-neutral-50">
            A Few Quirks
          </motion.h2>
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
            variants={ANIMATION_VARIANTS.staggerContainer}
          >
            {FUN_FACTS.map(fact => (
              <SpotlightCard key={fact.id} {...fact} />
            ))}
          </motion.div>
        </motion.section>

        {/* --- Connect Section with Pixel Runner --- */}
        <motion.footer
          className="relative text-center space-y-6 py-12 overflow-hidden rounded-3xl border border-neutral-800/50 bg-neutral-900/60 backdrop-blur-sm"
          variants={ANIMATION_VARIANTS.fadeInUp}
          initial="initial"
          animate="animate"
        >
          {/* Pixel Runner Animation */}
          <PixelRunner />

          {/* Footer Content */}
          <div className="relative z-10">
            <motion.h2
              className="text-3xl sm:text-4xl font-bold tracking-tight text-neutral-50"
              variants={ANIMATION_VARIANTS.scaleIn}
            >
              Let's Connect
            </motion.h2>
            <motion.p
              className="text-neutral-400 max-w-xl mx-auto"
              variants={ANIMATION_VARIANTS.fadeInUp}
            >
              Intrigued? I'd love to chat about shared interests, new opportunities, or the
              fascinating intersection of technology and life.
            </motion.p>
            <motion.div className="flex justify-center mt-8" variants={ANIMATION_VARIANTS.scaleIn}>
              <Magnetic>
                <motion.a
                  href="/contact"
                  className="inline-block rounded-full bg-neutral-800 hover:bg-neutral-700 py-4 px-10 text-lg font-semibold text-white transition-all duration-300 shadow-lg hover:shadow-xl border border-neutral-700/50 relative overflow-hidden group"
                  whileHover={HOVER_SCALE}
                  whileTap={TAP_SCALE}
                >
                  {/* Button shimmer effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
                  Get in Touch
                </motion.a>
              </Magnetic>
            </motion.div>
          </div>
        </motion.footer>
      </div>
    </div>
  );
}
