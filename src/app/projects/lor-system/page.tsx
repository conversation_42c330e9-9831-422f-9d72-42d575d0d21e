import Image from "next/image";
import Link from "next/link";

export default function LORSystemPage() {
  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h1 className="text-3xl font-bold tracking-tight sm:text-4xl">Letter of Recommendation System</h1>
        <p className="text-[hsl(var(--muted-foreground))]">A digital system for managing Letter of Recommendation (LOR) applications using React.js, Microsoft Power Automate, SharePoint, and Azure SQL Database.</p>
      </div>

      <div className="relative aspect-video w-full max-w-3xl rounded-lg overflow-hidden">
        <Image
          src="/projects_images/lor_system.png"
          alt="LOR System"
          fill
          className="object-cover"
        />
      </div>

      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Overview</h2>
        <p>
          I developed a digital system for managing Letter of Recommendation (LOR) applications using React.js, 
          Microsoft Power Automate, SharePoint, and Azure SQL Database. This project streamlines the application 
          process for 15 departments, each with its own workflow, revolutionizing how students request and 
          administrators process recommendation letters.
        </p>
      </div>

      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Key Components</h2>
        <ul className="list-disc pl-5 space-y-2">
          <li>
            <strong>React.js Forms</strong>: Created user-friendly interfaces for students to submit LOR requests, 
            with data sent via POST requests and retrieved using GET requests.
          </li>
          <li>
            <strong>Microsoft Power Automate</strong>: Implemented automated workflows to route applications 
            through approval processes specific to each department.
          </li>
          <li>
            <strong>SharePoint Integration</strong>: Utilized self-hosted SharePoint for secure on-premises 
            storage of all application data and documents.
          </li>
          <li>
            <strong>Azure SQL Database</strong>: Designed a central repository for application data with 
            separate workflows for generating statistics and insights.
          </li>
          <li>
            <strong>Email Notifications</strong>: Built an automated system to send acceptance and rejection 
            emails to students regarding their LOR applications.
          </li>
        </ul>
      </div>

      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Impact</h2>
        <p>
          This digital transformation project was widely adopted by college officials and over 20,000 students, 
          streamlining communication between applicants and administration. The system received the Director's 
          commendation from Reva University for its significant contribution to improving administrative processes.
        </p>
      </div>

      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Technologies Used</h2>
        <div className="flex flex-wrap gap-2">
          <span className="rounded-full bg-[hsl(var(--secondary))] px-3 py-1 text-sm text-[hsl(var(--secondary-foreground))]">
            React.js
          </span>
          <span className="rounded-full bg-[hsl(var(--secondary))] px-3 py-1 text-sm text-[hsl(var(--secondary-foreground))]">
            Power Automate
          </span>
          <span className="rounded-full bg-[hsl(var(--secondary))] px-3 py-1 text-sm text-[hsl(var(--secondary-foreground))]">
            SharePoint
          </span>
          <span className="rounded-full bg-[hsl(var(--secondary))] px-3 py-1 text-sm text-[hsl(var(--secondary-foreground))]">
            Azure SQL
          </span>
        </div>
      </div>

      <div className="flex justify-start">
        <Link
          href="/projects"
          className="inline-flex items-center rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--card))] px-4 py-2 text-sm font-medium transition-colors hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))]"
        >
          ← Back to Projects
        </Link>
      </div>
    </div>
  );
}
