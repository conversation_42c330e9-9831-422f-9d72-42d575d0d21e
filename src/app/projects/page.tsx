import { Metadata } from "next";
import Link from "next/link";
import Image from "next/image";

export const metadata: Metadata = {
  title: "Projects - Thanmai Sai",
  description: "Portfolio of projects by <PERSON><PERSON><PERSON>, a Full-Stack Developer",
};

export default function ProjectsPage() {
  return (
    <div className="space-y-8">
      <header className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight sm:text-4xl">Projects</h1>
        <p className="text-[hsl(var(--muted-foreground))]">Crafting memorable digital experiences.</p>
      </header>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {/* Project Card 1 - LOR System */}
        <Link href="/projects/lor-system" className="group">
          <div className="overflow-hidden rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] transition-colors hover:border-[hsl(var(--accent))]">
            <div className="aspect-video w-full relative">
              <Image
                src="/projects_images/lor_system.png"
                alt="LOR System"
                fill
                className="object-cover"
              />
            </div>
            <div className="p-4">
              <h3 className="font-medium text-lg group-hover:underline">Letter of Recommendation System</h3>
              <p className="mt-2 text-sm text-[hsl(var(--muted-foreground))]">A digital system for managing LOR applications for university students.</p>
              <div className="mt-3 flex flex-wrap gap-2">
                <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                  React.js
                </span>
                <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                  Power Automate
                </span>
                <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                  SharePoint
                </span>
              </div>
            </div>
          </div>
        </Link>

        {/* Project Card 2 - HyperBooks */}
        <Link href="/projects/hyperbooks" className="group">
          <div className="overflow-hidden rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] transition-colors hover:border-[hsl(var(--accent))]">
            <div className="aspect-video w-full relative">
              <Image
                src="/projects_images/hyperbooks.png"
                alt="HyperBooks"
                fill
                className="object-cover"
              />
            </div>
            <div className="p-4">
              <h3 className="font-medium text-lg group-hover:underline">HyperBooks</h3>
              <p className="mt-2 text-sm text-[hsl(var(--muted-foreground))]">An AI-powered book recommendation and summary platform.</p>
              <div className="mt-3 flex flex-wrap gap-2">
                <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                  Next.js
                </span>
                <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                  OpenAI
                </span>
                <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                  MongoDB
                </span>
              </div>
            </div>
          </div>
        </Link>

        {/* Project Card 3 - Parsify */}
        <Link href="/projects/parsify" className="group">
          <div className="overflow-hidden rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] transition-colors hover:border-[hsl(var(--accent))]">
            <div className="aspect-video w-full relative">
              <Image
                src="/projects_images/parsify.png"
                alt="Parsify"
                fill
                className="object-cover"
              />
            </div>
            <div className="p-4">
              <h3 className="font-medium text-lg group-hover:underline">Parsify</h3>
              <p className="mt-2 text-sm text-[hsl(var(--muted-foreground))]">A document parsing tool that extracts structured data from PDFs and images.</p>
              <div className="mt-3 flex flex-wrap gap-2">
                <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                  Python
                </span>
                <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                  FastAPI
                </span>
                <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                  LangChain
                </span>
              </div>
            </div>
          </div>
        </Link>

        {/* Project Card 4 - Portfolio Website */}
        <Link href="/projects/portfolio-website" className="group">
          <div className="overflow-hidden rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] transition-colors hover:border-[hsl(var(--accent))]">
            <div className="aspect-video w-full relative">
              <Image
                src="/images/hyperfox_poster.png"
                alt="Portfolio Website"
                fill
                className="object-cover"
              />
            </div>
            <div className="p-4">
              <h3 className="font-medium text-lg group-hover:underline">Portfolio Website</h3>
              <p className="mt-2 text-sm text-[hsl(var(--muted-foreground))]">A modern, responsive portfolio website built with Next.js and Tailwind CSS.</p>
              <div className="mt-3 flex flex-wrap gap-2">
                <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                  Next.js
                </span>
                <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                  Tailwind
                </span>
                <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                  TypeScript
                </span>
              </div>
            </div>
          </div>
        </Link>
      </div>
    </div>
  );
}
