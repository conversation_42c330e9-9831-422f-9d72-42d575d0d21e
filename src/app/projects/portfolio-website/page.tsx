import {
  ArrowLeft,
  Calendar,
  Command,
  ExternalLink,
  Github,
  Mic,
  <PERSON><PERSON>,
  Shield,
  TrendingUp,
  <PERSON>,
  Zap,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

export default function PortfolioWebsitePage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Back Navigation */}
      <div className="px-6 pt-12 lg:px-8">
        <Link
          href="/projects"
          className="inline-flex items-center gap-3 text-sm font-medium text-muted-foreground hover:text-foreground transition-all duration-300 group"
        >
          <ArrowLeft className="h-4 w-4 transition-transform duration-300 group-hover:-translate-x-1" />
          <span>Back to Projects</span>
        </Link>
      </div>

      {/* Project Header & Context */}
      <section className="px-6 py-32 lg:px-8">
        <div className="mx-auto max-w-4xl">
          <div className="mb-16">
            <div className="flex flex-wrap items-center gap-3 mb-8">
              <span className="inline-flex items-center rounded-full bg-primary/8 px-4 py-2 text-sm font-medium text-primary border border-primary/20">
                Featured Project
              </span>
              <span className="inline-flex items-center rounded-full bg-accent/8 px-4 py-2 text-sm font-medium text-accent border border-accent/20">
                Full-Stack Development
              </span>
            </div>
            <h1 className="text-5xl font-semibold tracking-tight text-foreground sm:text-6xl mb-8 leading-[1.1] -tracking-[0.025em]">
              HyperFox Portfolio
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl leading-relaxed">
              A modern portfolio application built with Next.js 15 and React 19, featuring dynamic
              blog integration, voice assistant, accessibility compliance, and professional
              architecture patterns.
            </p>
          </div>

          {/* Project Metadata */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="group bg-card/50 p-6 rounded-xl border border-border/50 hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 hover:scale-[1.02]">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/15 transition-colors duration-300">
                  <Calendar className="h-4 w-4 text-primary" />
                </div>
                <span className="text-sm font-medium text-card-foreground">Timeline</span>
              </div>
              <p className="text-sm text-muted-foreground font-medium">3 months (2025)</p>
            </div>
            <div className="group bg-card/50 p-6 rounded-xl border border-border/50 hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-accent/5 hover:scale-[1.02]">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-lg bg-accent/10 group-hover:bg-accent/15 transition-colors duration-300">
                  <Users className="h-4 w-4 text-accent" />
                </div>
                <span className="text-sm font-medium text-card-foreground">Team</span>
              </div>
              <p className="text-sm text-muted-foreground font-medium">Solo Developer</p>
            </div>
            <div className="group bg-card/50 p-6 rounded-xl border border-border/50 hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-secondary/5 hover:scale-[1.02]">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-lg bg-secondary/10 group-hover:bg-secondary/15 transition-colors duration-300">
                  <Zap className="h-4 w-4 text-secondary" />
                </div>
                <span className="text-sm font-medium text-card-foreground">Core Web Vitals</span>
              </div>
              <p className="text-sm text-muted-foreground font-medium">95+ Lighthouse Score</p>
            </div>
            <div className="group bg-card/50 p-6 rounded-xl border border-border/50 hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-muted/5 hover:scale-[1.02]">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-lg bg-muted/20 group-hover:bg-muted/30 transition-colors duration-300">
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="text-sm font-medium text-card-foreground">Features</span>
              </div>
              <p className="text-sm text-muted-foreground font-medium">10+ Advanced Features</p>
            </div>
          </div>
        </div>
      </section>

      {/* Problem & Solution using STAR Method */}
      <section className="px-6 py-32 lg:px-8 bg-gradient-to-b from-card/20 to-card/5">
        <div className="mx-auto max-w-4xl">
          <h2 className="text-4xl font-semibold text-foreground mb-20 text-center tracking-tight -tracking-[0.025em]">Project Overview</h2>

          <div className="space-y-20">
            {/* Situation */}
            <div className="group">
              <h3 className="text-2xl font-medium text-foreground mb-6 flex items-center gap-4">
                <span className="w-12 h-12 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 text-primary flex items-center justify-center text-lg font-semibold border border-primary/20 group-hover:scale-110 transition-transform duration-300">
                  S
                </span>
                Situation
              </h3>
              <p className="text-muted-foreground pl-16 text-lg leading-relaxed max-w-2xl">
                Need for a portfolio that demonstrates advanced technical capabilities, modern
                development practices, and enterprise-grade features beyond typical static developer
                websites.
              </p>
            </div>

            {/* Task */}
            <div className="group">
              <h3 className="text-2xl font-medium text-foreground mb-6 flex items-center gap-4">
                <span className="w-12 h-12 rounded-2xl bg-gradient-to-br from-accent/10 to-accent/5 text-accent flex items-center justify-center text-lg font-semibold border border-accent/20 group-hover:scale-110 transition-transform duration-300">
                  T
                </span>
                Task
              </h3>
              <p className="text-muted-foreground pl-16 text-lg leading-relaxed max-w-2xl">
                Build a comprehensive portfolio showcasing full-stack expertise through practical
                implementations of modern architecture, API integrations, accessibility standards,
                and innovative user experiences.
              </p>
            </div>

            {/* Action */}
            <div className="group">
              <h3 className="text-2xl font-medium text-foreground mb-6 flex items-center gap-4">
                <span className="w-12 h-12 rounded-2xl bg-gradient-to-br from-secondary/10 to-secondary/5 text-secondary flex items-center justify-center text-lg font-semibold border border-secondary/20 group-hover:scale-110 transition-transform duration-300">
                  A
                </span>
                Action
              </h3>
              <div className="pl-16">
                <p className="text-muted-foreground mb-6 text-lg leading-relaxed max-w-2xl">
                  Developed a feature-rich application using cutting-edge technologies:
                </p>
                <ul className="space-y-4 text-base text-muted-foreground max-w-2xl">
                  <li className="flex items-start gap-4">
                    <div className="w-2 h-2 rounded-full bg-primary mt-3 flex-shrink-0"></div>
                    <span className="leading-relaxed">Next.js 15 App Router + React 19 for optimal performance</span>
                  </li>
                  <li className="flex items-start gap-4">
                    <div className="w-2 h-2 rounded-full bg-accent mt-3 flex-shrink-0"></div>
                    <span className="leading-relaxed">Dynamic blog via Medium RSS with caching and pagination</span>
                  </li>
                  <li className="flex items-start gap-4">
                    <div className="w-2 h-2 rounded-full bg-secondary mt-3 flex-shrink-0"></div>
                    <span className="leading-relaxed">Vapi AI voice assistant for interactive navigation</span>
                  </li>
                  <li className="flex items-start gap-4">
                    <div className="w-2 h-2 rounded-full bg-muted-foreground mt-3 flex-shrink-0"></div>
                    <span className="leading-relaxed">WCAG 2.1 AA compliance + global command menu (⌘K)</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Result */}
            <div className="group">
              <h3 className="text-2xl font-medium text-foreground mb-6 flex items-center gap-4">
                <span className="w-12 h-12 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 text-primary flex items-center justify-center text-lg font-semibold border border-primary/20 group-hover:scale-110 transition-transform duration-300">
                  R
                </span>
                Result
              </h3>
              <div className="pl-16">
                <p className="text-muted-foreground mb-8 text-lg leading-relaxed max-w-2xl">
                  Achieved measurable technical excellence and performance benchmarks:
                </p>
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="group/card bg-card/50 p-6 rounded-2xl border border-border/50 hover:border-primary/30 hover:shadow-lg hover:shadow-primary/5 transition-all duration-300 hover:scale-[1.02]">
                    <div className="text-3xl font-semibold text-primary mb-2 group-hover/card:scale-110 transition-transform duration-300">95+</div>
                    <div className="text-muted-foreground font-medium">Lighthouse Performance Score</div>
                  </div>
                  <div className="group/card bg-card/50 p-6 rounded-2xl border border-border/50 hover:border-accent/30 hover:shadow-lg hover:shadow-accent/5 transition-all duration-300 hover:scale-[1.02]">
                    <div className="text-3xl font-semibold text-accent mb-2 group-hover/card:scale-110 transition-transform duration-300">WCAG 2.1</div>
                    <div className="text-muted-foreground font-medium">AA Accessibility Compliance</div>
                  </div>
                  <div className="group/card bg-card/50 p-6 rounded-2xl border border-border/50 hover:border-secondary/30 hover:shadow-lg hover:shadow-secondary/5 transition-all duration-300 hover:scale-[1.02]">
                    <div className="text-3xl font-semibold text-secondary mb-2 group-hover/card:scale-110 transition-transform duration-300">10+</div>
                    <div className="text-muted-foreground font-medium">Advanced Features Implemented</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Technology Stack Section */}
      <section id="technology-stack" className="px-6 py-32 lg:px-8">
        <div className="mx-auto max-w-6xl">
          <div className="mb-24 text-center">
            <h2 className="mb-6 text-4xl font-semibold text-foreground md:text-5xl tracking-tight -tracking-[0.025em]">
              Technology Stack
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              Modern technologies and integrations powering this portfolio application
            </p>
          </div>

          {/* Core Technologies */}
          <div className="space-y-32">
            {/* Frontend Architecture */}
            <div className="grid items-center gap-16 md:grid-cols-2">
              <div>
                <div className="mb-8 flex items-center gap-4">
                  <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-primary to-primary/80 text-primary-foreground font-semibold text-xl shadow-lg shadow-primary/25">
                    1
                  </div>
                </div>
                <h3 className="mb-6 text-3xl font-semibold text-foreground leading-tight">
                  Next.js 15
                  <br />
                  <span className="text-primary">App Router</span>
                  <br />
                  <span className="text-primary">Architecture</span>
                </h3>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  Built with the latest Next.js 15 App Router and React 19 for optimal performance,
                  type safety, and developer experience.
                </p>
              </div>
              <div className="relative">
                <div className="rounded-2xl bg-card/50 p-8 border border-border/50 backdrop-blur-sm">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="group bg-card/80 p-6 rounded-xl border border-border/50 text-center hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 hover:scale-[1.02]">
                      <div className="mb-4 flex justify-center">
                        <div className="p-3 rounded-xl bg-background/50 group-hover:bg-background transition-colors duration-300">
                          <Image
                            src="https://www.svgrepo.com/show/369457/nextjs.svg"
                            alt="Next.js"
                            width={32}
                            height={32}
                            className="dark:invert"
                          />
                        </div>
                      </div>
                      <div className="font-semibold text-foreground mb-2">Next.js 15</div>
                      <div className="text-sm text-muted-foreground">
                        App Router, Static Generation
                      </div>
                    </div>
                    <div className="group bg-card/80 p-6 rounded-xl border border-border/50 text-center hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-accent/5 hover:scale-[1.02]">
                      <div className="mb-4 flex justify-center">
                        <div className="p-3 rounded-xl bg-background/50 group-hover:bg-background transition-colors duration-300">
                          <Image
                            src="https://www.svgrepo.com/show/354259/react.svg"
                            alt="React"
                            width={32}
                            height={32}
                          />
                        </div>
                      </div>
                      <div className="font-semibold text-foreground mb-2">React 19</div>
                      <div className="text-sm text-muted-foreground">
                        Concurrent features, hooks
                      </div>
                    </div>
                    <div className="group bg-card/80 p-6 rounded-xl border border-border/50 text-center hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-secondary/5 hover:scale-[1.02]">
                      <div className="mb-4 flex justify-center">
                        <div className="p-3 rounded-xl bg-background/50 group-hover:bg-background transition-colors duration-300">
                          <Image
                            src="https://www.svgrepo.com/show/374146/typescript-official.svg"
                            alt="TypeScript"
                            width={32}
                            height={32}
                          />
                        </div>
                      </div>
                      <div className="font-semibold text-foreground mb-2">TypeScript</div>
                      <div className="text-sm text-muted-foreground">Strict mode, type safety</div>
                    </div>
                    <div className="group bg-card/80 p-6 rounded-xl border border-border/50 text-center hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-muted/5 hover:scale-[1.02]">
                      <div className="mb-4 flex justify-center">
                        <div className="p-3 rounded-xl bg-background/50 group-hover:bg-background transition-colors duration-300">
                          <Image
                            src="https://www.svgrepo.com/show/374118/tailwind.svg"
                            alt="Tailwind CSS"
                            width={32}
                            height={32}
                          />
                        </div>
                      </div>
                      <div className="font-semibold text-foreground mb-2">Tailwind CSS</div>
                      <div className="text-sm text-muted-foreground">Utility-first styling</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* API Integrations */}
            <div className="grid items-center gap-16 md:grid-cols-2">
              <div className="order-2 md:order-1">
                <div className="rounded-2xl bg-card/50 p-8 border border-border/50 backdrop-blur-sm">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="group bg-card/80 p-6 rounded-xl border border-border/50 text-center hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 hover:scale-[1.02]">
                      <div className="mb-4 flex justify-center">
                        <div className="p-3 rounded-xl bg-background/50 group-hover:bg-background transition-colors duration-300">
                          <Image
                            src="https://www.svgrepo.com/show/342027/medium-m.svg"
                            alt="Medium"
                            width={32}
                            height={32}
                          />
                        </div>
                      </div>
                      <div className="font-semibold text-foreground mb-2">Medium RSS</div>
                      <div className="text-sm text-muted-foreground">Dynamic blog integration</div>
                    </div>
                    <div className="group bg-card/80 p-6 rounded-xl border border-border/50 text-center hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-accent/5 hover:scale-[1.02]">
                      <div className="mb-4 flex justify-center">
                        <div className="p-3 rounded-xl bg-gradient-to-r from-green-500 to-blue-500 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                          <span className="text-white font-bold text-sm">E</span>
                        </div>
                      </div>
                      <div className="font-semibold text-foreground mb-2">EmailJS</div>
                      <div className="text-sm text-muted-foreground">Contact form handling</div>
                    </div>
                    <div className="group bg-card/80 p-6 rounded-xl border border-border/50 text-center hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-secondary/5 hover:scale-[1.02]">
                      <div className="mb-4 flex justify-center">
                        <div className="p-3 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                          <span className="text-white font-bold text-sm">V</span>
                        </div>
                      </div>
                      <div className="font-semibold text-foreground mb-2">Vapi AI</div>
                      <div className="text-sm text-muted-foreground">Voice assistant</div>
                    </div>
                    <div className="group bg-card/80 p-6 rounded-xl border border-border/50 text-center hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-muted/5 hover:scale-[1.02]">
                      <div className="mb-4 flex justify-center">
                        <div className="p-3 rounded-xl bg-background/50 group-hover:bg-background transition-colors duration-300">
                          <Image
                            src="https://www.svgrepo.com/show/353383/airtable.svg"
                            alt="Airtable"
                            width={32}
                            height={32}
                          />
                        </div>
                      </div>
                      <div className="font-semibold text-foreground mb-2">Airtable</div>
                      <div className="text-sm text-muted-foreground">Tech stack data</div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="order-1 md:order-2">
                <div className="mb-8 flex items-center gap-4">
                  <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-accent to-accent/80 text-accent-foreground font-semibold text-xl shadow-lg shadow-accent/25">
                    2
                  </div>
                </div>
                <h3 className="mb-6 text-3xl font-semibold text-foreground leading-tight">
                  API Integrations
                  <br />
                  <span className="text-accent">& External</span> Services
                </h3>
                <p className="text-lg text-muted-foreground leading-relaxed">
                  Seamless integrations with external services for dynamic content, communication,
                  and AI-powered features.
                </p>
              </div>
            </div>

            {/* Advanced Features */}
            <div className="grid items-center gap-16 md:grid-cols-2">
              <div>
                <div className="mb-8 flex items-center gap-4">
                  <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-secondary to-secondary/80 text-secondary-foreground font-semibold text-xl shadow-lg shadow-secondary/25">
                    3
                  </div>
                </div>
                <h3 className="mb-6 text-3xl font-semibold text-foreground leading-tight">
                  Advanced
                  <br />
                  <span className="text-secondary">Features</span> &
                  <br />
                  <span className="text-secondary">UX Patterns</span>
                </h3>
                <p className="mb-10 text-lg text-muted-foreground leading-relaxed">
                  Modern UX patterns including command palette, voice interactions, and
                  accessibility features for enhanced user experience.
                </p>

                {/* Features Grid */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="group rounded-xl bg-card/50 p-5 border border-border/50 hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 hover:scale-[1.02]">
                    <div className="mb-3 flex items-center gap-3">
                      <div className="h-8 w-8 rounded-xl bg-primary/10 flex items-center justify-center text-primary group-hover:bg-primary/15 transition-colors duration-300">
                        <Command className="h-4 w-4" />
                      </div>
                      <h4 className="font-semibold text-card-foreground">Command Menu</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">Global navigation palette (⌘K)</p>
                  </div>
                  <div className="group rounded-xl bg-card/50 p-5 border border-border/50 hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-accent/5 hover:scale-[1.02]">
                    <div className="mb-3 flex items-center gap-3">
                      <div className="h-8 w-8 rounded-xl bg-accent/10 flex items-center justify-center text-accent group-hover:bg-accent/15 transition-colors duration-300">
                        <Mic className="h-4 w-4" />
                      </div>
                      <h4 className="font-semibold text-card-foreground">Voice Assistant</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">AI-powered voice interactions</p>
                  </div>
                  <div className="group rounded-xl bg-card/50 p-5 border border-border/50 hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-secondary/5 hover:scale-[1.02]">
                    <div className="mb-3 flex items-center gap-3">
                      <div className="h-8 w-8 rounded-xl bg-secondary/10 flex items-center justify-center text-secondary group-hover:bg-secondary/15 transition-colors duration-300">
                        <Shield className="h-4 w-4" />
                      </div>
                      <h4 className="font-semibold text-card-foreground">WCAG 2.1 AA</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">Full accessibility compliance</p>
                  </div>
                  <div className="group rounded-xl bg-card/50 p-5 border border-border/50 hover:border-border transition-all duration-300 hover:shadow-lg hover:shadow-muted/5 hover:scale-[1.02]">
                    <div className="mb-3 flex items-center gap-3">
                      <div className="h-8 w-8 rounded-xl bg-muted/20 flex items-center justify-center text-muted-foreground group-hover:bg-muted/30 transition-colors duration-300">
                        <Palette className="h-4 w-4" />
                      </div>
                      <h4 className="font-semibold text-card-foreground">Theme System</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">Light, dark, system themes</p>
                  </div>
                </div>
              </div>
              <div className="relative">
                <div className="rounded-2xl bg-card/50 p-8 border border-border/50 backdrop-blur-sm">
                  <div className="space-y-6">
                    <div className="bg-card/80 p-6 rounded-xl border border-border/50">
                      <h4 className="font-semibold text-foreground mb-4 text-lg">
                        Performance Optimizations
                      </h4>
                      <div className="space-y-3 text-sm text-muted-foreground">
                        <div className="flex items-start gap-3">
                          <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                          <span>Static Generation for optimal loading</span>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-1.5 h-1.5 rounded-full bg-accent mt-2 flex-shrink-0"></div>
                          <span>Code splitting with dynamic imports</span>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-1.5 h-1.5 rounded-full bg-secondary mt-2 flex-shrink-0"></div>
                          <span>Image optimization with Next.js Image</span>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-1.5 h-1.5 rounded-full bg-muted-foreground mt-2 flex-shrink-0"></div>
                          <span>Bundle size monitoring and optimization</span>
                        </div>
                      </div>
                    </div>
                    <div className="bg-card/80 p-6 rounded-xl border border-border/50">
                      <h4 className="font-semibold text-foreground mb-4 text-lg">
                        Accessibility (WCAG 2.1 AA)
                      </h4>
                      <div className="space-y-3 text-sm text-muted-foreground">
                        <div className="flex items-start gap-3">
                          <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                          <span>Semantic HTML and ARIA labels</span>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-1.5 h-1.5 rounded-full bg-accent mt-2 flex-shrink-0"></div>
                          <span>Full keyboard navigation support</span>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-1.5 h-1.5 rounded-full bg-secondary mt-2 flex-shrink-0"></div>
                          <span>Screen reader compatibility</span>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-1.5 h-1.5 rounded-full bg-muted-foreground mt-2 flex-shrink-0"></div>
                          <span>Focus management and color contrast</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Project Demo Section */}
      <section className="px-6 py-32 lg:px-8 bg-gradient-to-b from-background to-card/10">
        <div className="mx-auto max-w-6xl">
          <div className="mb-20 text-center">
            <h2 className="mb-6 text-4xl font-semibold text-foreground md:text-5xl tracking-tight -tracking-[0.025em]">Project Demo</h2>
            <p className="mx-auto max-w-2xl text-xl text-muted-foreground leading-relaxed">
              Interactive walkthrough showcasing voice assistant, command menu, and responsive
              design
            </p>
          </div>

          <div className="group relative rounded-2xl border border-border/50 overflow-hidden shadow-2xl shadow-black/10 hover:shadow-black/20 transition-all duration-500 hover:scale-[1.01]">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="relative pb-[56.25%]">
              <iframe
                className="absolute top-0 left-0 w-full h-full"
                src="https://www.loom.com/embed/313bf71d20ca47b2a35b6634cefdb761?hide_owner=true&hide_share=true&hide_title=true&hideEmbedTopBar=true&default_speed=true&skip_embed_eovn=true&t=0&disable_analytics=true"
                title="Portfolio Website Demo"
                style={{ border: 'none' }}
                allowFullScreen
              ></iframe>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      {/* <section className="px-4 py-20">
        <div className="mx-auto max-w-6xl">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-4xl font-bold text-foreground md:text-5xl">Gallery</h2>
            <p className="text-lg text-muted-foreground">
              Here are a few visuals created on Portfolio.
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4 md:grid-cols-4 lg:grid-cols-7">
            {Array.from({ length: 3 }).map((_, i) => (
              <div
                key={i}
                className="aspect-square rounded-lg bg-gradient-to-br from-primary/70 to-accent/70 border border-border hover:scale-105 transition-transform duration-200"
              ></div>
            ))}
          </div>

          <div className="mt-8 grid grid-cols-2 gap-4 md:grid-cols-4 lg:grid-cols-7">
            {Array.from({ length: 2 }).map((_, i) => (
              <div
                key={i}
                className="aspect-square rounded-lg bg-gradient-to-br from-accent/70 to-secondary/70 border border-border hover:scale-105 transition-transform duration-200"
              ></div>
            ))}
          </div>
        </div>
      </section> */}

      {/* CTA Section */}
      <section className="px-6 py-32 lg:px-8 text-center border-t border-border/30">
        <div className="mx-auto max-w-4xl">
          <h2 className="mb-8 text-4xl font-semibold text-foreground md:text-5xl tracking-tight -tracking-[0.025em]">
            Explore the Portfolio
          </h2>
          <p className="mb-12 text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto">
            Experience the live application with all its features, or dive into the source code to
            explore the implementation details and architecture.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              href="/"
              className="group inline-flex h-14 items-center justify-center rounded-2xl bg-gradient-to-r from-primary to-primary/90 px-8 text-base font-medium text-primary-foreground transition-all duration-300 hover:shadow-lg hover:shadow-primary/25 hover:scale-[1.02]"
            >
              <span>Visit Live Portfolio</span>
              <ExternalLink className="ml-3 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
            <Link
              href="https://github.com/thanmaisai/hyperfox-portfolio"
              target="_blank"
              rel="noopener noreferrer"
              className="group inline-flex h-14 items-center justify-center rounded-2xl border border-border/50 bg-card/50 px-8 text-base font-medium text-card-foreground transition-all duration-300 hover:bg-card hover:border-border hover:shadow-lg hover:shadow-black/5 hover:scale-[1.02] backdrop-blur-sm"
            >
              <Github className="mr-3 h-4 w-4" />
              <span>View Source Code</span>
            </Link>
          </div>

          {/* Project Navigation */}
          <div className="mt-16 pt-12 border-t border-border/30 flex flex-col sm:flex-row items-center justify-between gap-6">
            <Link
              href="/projects"
              className="group inline-flex items-center gap-3 text-muted-foreground hover:text-foreground transition-all duration-300"
            >
              <ArrowLeft className="h-4 w-4 transition-transform duration-300 group-hover:-translate-x-1" />
              <span className="font-medium">Back to All Projects</span>
            </Link>

            <div className="flex items-center gap-6">
              <Link
                href="/projects/lor-system"
                className="text-sm text-muted-foreground hover:text-foreground transition-all duration-300 font-medium"
              >
                ← Previous: LOR System
              </Link>
              <div className="w-px h-5 bg-border/50"></div>
              <Link
                href="/projects/hyperbooks"
                className="text-sm text-muted-foreground hover:text-foreground transition-all duration-300 font-medium"
              >
                Next: HyperBooks →
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
