import {
  ArrowLeft,
  Calendar,
  Command,
  ExternalLink,
  Github,
  Mic,
  Palette,
  Shield,
  TrendingUp,
  <PERSON>,
  Zap,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

export default function PortfolioWebsitePage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Back Navigation */}
      <div className="px-4 pt-8">
        <Link
          href="/projects"
          className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors group"
        >
          <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
          <span>Back to Projects</span>
        </Link>
      </div>

      {/* Project Header & Context */}
      <section className="px-4 py-12">
        <div className="mx-auto max-w-6xl">
          <div className="mb-8">
            <div className="flex flex-wrap items-center gap-4 mb-4">
              <span className="inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary">
                Featured Project
              </span>
              <span className="inline-flex items-center rounded-full bg-accent/10 px-3 py-1 text-sm font-medium text-accent">
                Full-Stack Development
              </span>
            </div>
            <h1 className="text-4xl font-bold tracking-tight text-foreground sm:text-5xl mb-4">
              HyperFox Portfolio
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl">
              A modern portfolio application built with Next.js 15 and React 19, featuring dynamic
              blog integration, voice assistant, accessibility compliance, and professional
              architecture patterns.
            </p>
          </div>

          {/* Project Metadata */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            <div className="bg-card p-4 rounded-lg border border-border">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium text-card-foreground">Timeline</span>
              </div>
              <p className="text-sm text-muted-foreground">3 months (2025)</p>
            </div>
            <div className="bg-card p-4 rounded-lg border border-border">
              <div className="flex items-center gap-2 mb-2">
                <Users className="h-4 w-4 text-accent" />
                <span className="text-sm font-medium text-card-foreground">Team</span>
              </div>
              <p className="text-sm text-muted-foreground">Solo Developer</p>
            </div>
            <div className="bg-card p-4 rounded-lg border border-border">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="h-4 w-4 text-secondary" />
                <span className="text-sm font-medium text-card-foreground">Core Web Vitals</span>
              </div>
              <p className="text-sm text-muted-foreground">95+ Lighthouse Score</p>
            </div>
            <div className="bg-card p-4 rounded-lg border border-border">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="h-4 w-4 text-muted" />
                <span className="text-sm font-medium text-card-foreground">Features</span>
              </div>
              <p className="text-sm text-muted-foreground">10+ Advanced Features</p>
            </div>
          </div>
        </div>
      </section>

      {/* Problem & Solution using STAR Method */}
      <section className="px-4 py-16 bg-card/30">
        <div className="mx-auto max-w-4xl">
          <h2 className="text-3xl font-bold text-foreground mb-12 text-center">Project Overview</h2>

          <div className="space-y-12">
            {/* Situation */}
            <div>
              <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
                <span className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center text-sm font-bold">
                  S
                </span>
                Situation
              </h3>
              <p className="text-muted-foreground pl-10">
                Need for a portfolio that demonstrates advanced technical capabilities, modern
                development practices, and enterprise-grade features beyond typical static developer
                websites.
              </p>
            </div>

            {/* Task */}
            <div>
              <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
                <span className="w-8 h-8 rounded-full bg-accent/10 text-accent flex items-center justify-center text-sm font-bold">
                  T
                </span>
                Task
              </h3>
              <p className="text-muted-foreground pl-10">
                Build a comprehensive portfolio showcasing full-stack expertise through practical
                implementations of modern architecture, API integrations, accessibility standards,
                and innovative user experiences.
              </p>
            </div>

            {/* Action */}
            <div>
              <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
                <span className="w-8 h-8 rounded-full bg-secondary/10 text-secondary flex items-center justify-center text-sm font-bold">
                  A
                </span>
                Action
              </h3>
              <div className="pl-10">
                <p className="text-muted-foreground mb-4">
                  Developed a feature-rich application using cutting-edge technologies:
                </p>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                    <span>Next.js 15 App Router + React 19 for optimal performance</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-accent mt-2 flex-shrink-0"></div>
                    <span>Dynamic blog via Medium RSS with caching and pagination</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-secondary mt-2 flex-shrink-0"></div>
                    <span>Vapi AI voice assistant for interactive navigation</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-muted mt-2 flex-shrink-0"></div>
                    <span>WCAG 2.1 AA compliance + global command menu (⌘K)</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Result */}
            <div>
              <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
                <span className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center text-sm font-bold">
                  R
                </span>
                Result
              </h3>
              <div className="pl-10">
                <p className="text-muted-foreground mb-4">
                  Achieved measurable technical excellence and performance benchmarks:
                </p>
                <div className="grid md:grid-cols-3 gap-4 text-sm">
                  <div className="bg-card p-4 rounded-lg border border-border">
                    <div className="text-2xl font-bold text-primary mb-1">95+</div>
                    <div className="text-muted-foreground">Lighthouse Performance Score</div>
                  </div>
                  <div className="bg-card p-4 rounded-lg border border-border">
                    <div className="text-2xl font-bold text-accent mb-1">WCAG 2.1</div>
                    <div className="text-muted-foreground">AA Accessibility Compliance</div>
                  </div>
                  <div className="bg-card p-4 rounded-lg border border-border">
                    <div className="text-2xl font-bold text-secondary mb-1">10+</div>
                    <div className="text-muted-foreground">Advanced Features Implemented</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Technology Stack Section */}
      <section id="technology-stack" className="px-4 py-20">
        <div className="mx-auto max-w-6xl">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-4xl font-bold text-foreground md:text-5xl">
              Technology Stack
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Modern technologies and integrations powering this portfolio application
            </p>
          </div>

          {/* Core Technologies */}
          <div className="space-y-24">
            {/* Frontend Architecture */}
            <div className="grid items-center gap-12 md:grid-cols-2">
              <div>
                <div className="mb-4 flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-primary-foreground font-bold">
                    1
                  </div>
                </div>
                <h3 className="mb-4 text-3xl font-bold text-foreground">
                  Next.js 15
                  <br />
                  <span className="text-primary">App Router</span>
                  <br />
                  <span className="text-primary">Architecture</span>
                </h3>
                <p className="text-lg text-muted-foreground">
                  Built with the latest Next.js 15 App Router and React 19 for optimal performance,
                  type safety, and developer experience.
                </p>
              </div>
              <div className="relative">
                <div className="rounded-lg bg-card p-8 border border-border">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-card p-4 rounded-lg border border-border text-center">
                      <div className="mb-3 flex justify-center">
                        <Image
                          src="https://www.svgrepo.com/show/369457/nextjs.svg"
                          alt="Next.js"
                          width={32}
                          height={32}
                          className="dark:invert"
                        />
                      </div>
                      <div className="font-semibold text-foreground mb-2">Next.js 15</div>
                      <div className="text-sm text-muted-foreground">
                        App Router, Static Generation
                      </div>
                    </div>
                    <div className="bg-card p-4 rounded-lg border border-border text-center">
                      <div className="mb-3 flex justify-center">
                        <Image
                          src="https://www.svgrepo.com/show/354259/react.svg"
                          alt="React"
                          width={32}
                          height={32}
                        />
                      </div>
                      <div className="font-semibold text-foreground mb-2">React 19</div>
                      <div className="text-sm text-muted-foreground">
                        Concurrent features, hooks
                      </div>
                    </div>
                    <div className="bg-card p-4 rounded-lg border border-border text-center">
                      <div className="mb-3 flex justify-center">
                        <Image
                          src="https://www.svgrepo.com/show/374146/typescript-official.svg"
                          alt="TypeScript"
                          width={32}
                          height={32}
                        />
                      </div>
                      <div className="font-semibold text-foreground mb-2">TypeScript</div>
                      <div className="text-sm text-muted-foreground">Strict mode, type safety</div>
                    </div>
                    <div className="bg-card p-4 rounded-lg border border-border text-center">
                      <div className="mb-3 flex justify-center">
                        <Image
                          src="https://www.svgrepo.com/show/374118/tailwind.svg"
                          alt="Tailwind CSS"
                          width={32}
                          height={32}
                        />
                      </div>
                      <div className="font-semibold text-foreground mb-2">Tailwind CSS</div>
                      <div className="text-sm text-muted-foreground">Utility-first styling</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* API Integrations */}
            <div className="grid items-center gap-12 md:grid-cols-2">
              <div className="order-2 md:order-1">
                <div className="rounded-lg bg-card p-8 border border-border">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-card p-4 rounded-lg border border-border text-center">
                      <div className="mb-3 flex justify-center">
                        <Image
                          src="https://www.svgrepo.com/show/342027/medium-m.svg"
                          alt="Medium"
                          width={32}
                          height={32}
                        />
                      </div>
                      <div className="font-semibold text-foreground mb-2">Medium RSS</div>
                      <div className="text-sm text-muted-foreground">Dynamic blog integration</div>
                    </div>
                    <div className="bg-card p-4 rounded-lg border border-border text-center">
                      <div className="mb-3 flex justify-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                          E
                        </div>
                      </div>
                      <div className="font-semibold text-foreground mb-2">EmailJS</div>
                      <div className="text-sm text-muted-foreground">Contact form handling</div>
                    </div>
                    <div className="bg-card p-4 rounded-lg border border-border text-center">
                      <div className="mb-3 flex justify-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                          V
                        </div>
                      </div>
                      <div className="font-semibold text-foreground mb-2">Vapi AI</div>
                      <div className="text-sm text-muted-foreground">Voice assistant</div>
                    </div>
                    <div className="bg-card p-4 rounded-lg border border-border text-center">
                      <div className="mb-3 flex justify-center">
                        <Image
                          src="https://www.svgrepo.com/show/353383/airtable.svg"
                          alt="Airtable"
                          width={32}
                          height={32}
                        />
                      </div>
                      <div className="font-semibold text-foreground mb-2">Airtable</div>
                      <div className="text-sm text-muted-foreground">Tech stack data</div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="order-1 md:order-2">
                <div className="mb-4 flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-accent text-accent-foreground font-bold">
                    2
                  </div>
                </div>
                <h3 className="mb-4 text-3xl font-bold text-foreground">
                  API Integrations
                  <br />
                  <span className="text-accent">& External</span> Services
                </h3>
                <p className="text-lg text-muted-foreground">
                  Seamless integrations with external services for dynamic content, communication,
                  and AI-powered features.
                </p>
              </div>
            </div>

            {/* Advanced Features */}
            <div className="grid items-center gap-12 md:grid-cols-2">
              <div>
                <div className="mb-4 flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-secondary text-secondary-foreground font-bold">
                    3
                  </div>
                </div>
                <h3 className="mb-4 text-3xl font-bold text-foreground">
                  Advanced
                  <br />
                  <span className="text-secondary">Features</span> &
                  <br />
                  <span className="text-secondary">UX Patterns</span>
                </h3>
                <p className="mb-8 text-lg text-muted-foreground">
                  Modern UX patterns including command palette, voice interactions, and
                  accessibility features for enhanced user experience.
                </p>

                {/* Features Grid */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="rounded-lg bg-card p-4 shadow-sm border border-border">
                    <div className="mb-2 flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
                        <Command className="h-3 w-3" />
                      </div>
                      <h4 className="font-semibold text-card-foreground text-sm">Command Menu</h4>
                    </div>
                    <p className="text-xs text-muted-foreground">Global navigation palette (⌘K)</p>
                  </div>
                  <div className="rounded-lg bg-card p-4 shadow-sm border border-border">
                    <div className="mb-2 flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-accent flex items-center justify-center text-accent-foreground">
                        <Mic className="h-3 w-3" />
                      </div>
                      <h4 className="font-semibold text-card-foreground text-sm">
                        Voice Assistant
                      </h4>
                    </div>
                    <p className="text-xs text-muted-foreground">AI-powered voice interactions</p>
                  </div>
                  <div className="rounded-lg bg-card p-4 shadow-sm border border-border">
                    <div className="mb-2 flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-secondary flex items-center justify-center text-secondary-foreground">
                        <Shield className="h-3 w-3" />
                      </div>
                      <h4 className="font-semibold text-card-foreground text-sm">WCAG 2.1 AA</h4>
                    </div>
                    <p className="text-xs text-muted-foreground">Full accessibility compliance</p>
                  </div>
                  <div className="rounded-lg bg-card p-4 shadow-sm border border-border">
                    <div className="mb-2 flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-muted-foreground">
                        <Palette className="h-3 w-3" />
                      </div>
                      <h4 className="font-semibold text-card-foreground text-sm">Theme System</h4>
                    </div>
                    <p className="text-xs text-muted-foreground">Light, dark, system themes</p>
                  </div>
                </div>
              </div>
              <div className="relative">
                <div className="rounded-lg bg-card p-8 border border-border">
                  <div className="space-y-4">
                    <div className="bg-card/50 p-4 rounded-lg border border-border">
                      <h4 className="font-semibold text-foreground mb-3">
                        Performance Optimizations
                      </h4>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <div>• Static Generation for optimal loading</div>
                        <div>• Code splitting with dynamic imports</div>
                        <div>• Image optimization with Next.js Image</div>
                        <div>• Bundle size monitoring and optimization</div>
                      </div>
                    </div>
                    <div className="bg-card/50 p-4 rounded-lg border border-border">
                      <h4 className="font-semibold text-foreground mb-3">
                        Accessibility (WCAG 2.1 AA)
                      </h4>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <div>• Semantic HTML and ARIA labels</div>
                        <div>• Full keyboard navigation support</div>
                        <div>• Screen reader compatibility</div>
                        <div>• Focus management and color contrast</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Project Demo Section */}
      <section className="px-4 py-20">
        <div className="mx-auto max-w-6xl">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-4xl font-bold text-foreground md:text-5xl">Project Demo</h2>
            <p className="mx-auto max-w-3xl text-lg text-muted-foreground">
              Interactive walkthrough showcasing voice assistant, command menu, and responsive
              design
            </p>
          </div>

          <div className="rounded-lg border border-border overflow-hidden">
            <div className="relative pb-[56.25%]">
              <iframe
                className="absolute top-0 left-0 w-full h-full"
                src="https://www.loom.com/embed/313bf71d20ca47b2a35b6634cefdb761?hide_owner=true&hide_share=true&hide_title=true&hideEmbedTopBar=true&default_speed=true&skip_embed_eovn=true&t=0&disable_analytics=true"
                title="Portfolio Website Demo"
                frameBorder="0"
                allowFullScreen
              ></iframe>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      {/* <section className="px-4 py-20">
        <div className="mx-auto max-w-6xl">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-4xl font-bold text-foreground md:text-5xl">Gallery</h2>
            <p className="text-lg text-muted-foreground">
              Here are a few visuals created on Portfolio.
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4 md:grid-cols-4 lg:grid-cols-7">
            {Array.from({ length: 3 }).map((_, i) => (
              <div
                key={i}
                className="aspect-square rounded-lg bg-gradient-to-br from-primary/70 to-accent/70 border border-border hover:scale-105 transition-transform duration-200"
              ></div>
            ))}
          </div>

          <div className="mt-8 grid grid-cols-2 gap-4 md:grid-cols-4 lg:grid-cols-7">
            {Array.from({ length: 2 }).map((_, i) => (
              <div
                key={i}
                className="aspect-square rounded-lg bg-gradient-to-br from-accent/70 to-secondary/70 border border-border hover:scale-105 transition-transform duration-200"
              ></div>
            ))}
          </div>
        </div>
      </section> */}

      {/* CTA Section */}
      <section className="px-4 py-20 text-center border-t border-border">
        <div className="mx-auto max-w-4xl">
          <h2 className="mb-6 text-4xl font-bold text-foreground md:text-5xl">
            Explore the Portfolio
          </h2>
          <p className="mb-8 text-lg text-muted-foreground">
            Experience the live application with all its features, or dive into the source code to
            explore the implementation details and architecture.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="inline-flex h-12 items-center justify-center rounded-md bg-primary px-8 text-base font-medium text-primary-foreground transition-colors hover:bg-accent hover:text-accent-foreground group"
            >
              <span>Visit Live Portfolio</span>
              <ExternalLink className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Link>
            <Link
              href="https://github.com/thanmaisai/hyperfox-portfolio"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex h-12 items-center justify-center rounded-md border border-border bg-card px-8 text-base font-medium text-card-foreground transition-colors hover:bg-accent hover:text-accent-foreground group"
            >
              <Github className="mr-2 h-4 w-4" />
              <span>View Source Code</span>
            </Link>
          </div>

          {/* Project Navigation */}
          <div className="mt-12 pt-8 border-t border-border/50 flex flex-col sm:flex-row items-center justify-between gap-4">
            <Link
              href="/projects"
              className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors group"
            >
              <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
              <span>Back to All Projects</span>
            </Link>

            <div className="flex items-center gap-4">
              <Link
                href="/projects/lor-system"
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                ← Previous: LOR System
              </Link>
              <div className="w-px h-4 bg-border"></div>
              <Link
                href="/projects/hyperbooks"
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                Next: HyperBooks →
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
