import {
  ArrowLeft,
  Bot,
  CheckCircle,
  Code,
  Database,
  ExternalLink,
  Github,
  Globe,
  Mail,
  Palette,
  Rss,
  Shield,
  Target,
  TrendingUp,
  Zap,
} from 'lucide-react';
import Link from 'next/link';

export default function PortfolioWebsitePage() {
  return (
    <div className="min-h-screen bg-background">
      {/* --- BACK NAVIGATION --- */}
      <div className="px-4 pt-8">
        <Link
          href="/projects"
          className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors group"
        >
          <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
          <span>Back to Projects</span>
        </Link>
      </div>

      {/* --- SECTION 1: PROJECT HEADER & CONTEXT --- */}
      <section className="px-4 py-16 text-center">
        <div className="mx-auto max-w-4xl">
          <div className="flex justify-center flex-wrap items-center gap-4 mb-8">
            <span className="inline-flex items-center rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary border border-primary/20">
              Featured Project
            </span>
            <span className="inline-flex items-center rounded-full bg-accent/10 px-4 py-2 text-sm font-medium text-accent border border-accent/20">
              Full-Stack Development
            </span>
            <span className="inline-flex items-center rounded-full bg-secondary/10 px-4 py-2 text-sm font-medium text-secondary border border-secondary/20">
              Next.js 15
            </span>
          </div>
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl mb-6 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
            HyperFox Portfolio: A Digital Masterpiece
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            An immersive portfolio experience engineered with Next.js 15, seamlessly blending an AI
            voice assistant, dynamic blog, and uncompromising accessibility standards.
          </p>
        </div>
      </section>

      {/* --- SECTION 2: PROJECT DEMO --- */}
      <section className="px-4 pb-20">
        <div className="mx-auto max-w-6xl">
          <div className="rounded-2xl border border-border overflow-hidden shadow-2xl shadow-primary/10 bg-card backdrop-blur-sm">
            <div className="relative pb-[56.25%] bg-gradient-to-br from-primary/5 to-accent/5">
              <iframe
                className="absolute top-0 left-0 w-full h-full"
                src="https://www.loom.com/embed/313bf71d20ca47b2a35b6634cefdb761?hide_owner=true&hide_share=true&hide_title=true&hideEmbedTopBar=true"
                title="Portfolio Website Demo - Interactive walkthrough of the HyperFox Portfolio features"
                frameBorder="0"
                allowFullScreen
                loading="lazy"
              ></iframe>
            </div>
          </div>
          <p className="text-center text-sm text-muted-foreground mt-4 max-w-2xl mx-auto">
            Interactive demo showcasing the portfolio's key features including AI voice assistant,
            dynamic blog integration, and accessibility features.
          </p>
        </div>
      </section>

      {/* --- SECTION 3: KEY ACHIEVEMENTS --- */}
      <section className="px-4 py-20 bg-gradient-to-br from-muted/30 to-muted/70">
        <div className="mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
              Key Achievements
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              Measurable outcomes demonstrating technical excellence and a commitment to quality.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
            <div className="bg-card/80 backdrop-blur-sm p-6 lg:p-8 rounded-xl border border-border/50 transform hover:-translate-y-2 hover:shadow-xl transition-all duration-300 group">
              <div className="flex flex-col items-center text-center">
                <div className="p-3 rounded-full bg-primary/10 mb-6 group-hover:bg-primary/20 transition-colors">
                  <Zap className="h-8 w-8 text-primary" />
                </div>
                <div className="text-4xl lg:text-5xl font-bold text-foreground mb-2">95+</div>
                <p className="text-muted-foreground text-base lg:text-lg font-medium">
                  Lighthouse Performance
                </p>
                <p className="text-sm text-muted-foreground/70 mt-2">
                  Optimized for speed and efficiency
                </p>
              </div>
            </div>
            <div className="bg-card/80 backdrop-blur-sm p-6 lg:p-8 rounded-xl border border-border/50 transform hover:-translate-y-2 hover:shadow-xl transition-all duration-300 group">
              <div className="flex flex-col items-center text-center">
                <div className="p-3 rounded-full bg-accent/10 mb-6 group-hover:bg-accent/20 transition-colors">
                  <Shield className="h-8 w-8 text-accent" />
                </div>
                <div className="text-4xl lg:text-5xl font-bold text-foreground mb-2">AA</div>
                <p className="text-muted-foreground text-base lg:text-lg font-medium">
                  WCAG 2.1 Compliance
                </p>
                <p className="text-sm text-muted-foreground/70 mt-2">Fully accessible design</p>
              </div>
            </div>
            <div className="bg-card/80 backdrop-blur-sm p-6 lg:p-8 rounded-xl border border-border/50 transform hover:-translate-y-2 hover:shadow-xl transition-all duration-300 group">
              <div className="flex flex-col items-center text-center">
                <div className="p-3 rounded-full bg-secondary/10 mb-6 group-hover:bg-secondary/20 transition-colors">
                  <TrendingUp className="h-8 w-8 text-secondary" />
                </div>
                <div className="text-4xl lg:text-5xl font-bold text-foreground mb-2">15+</div>
                <p className="text-muted-foreground text-base lg:text-lg font-medium">
                  Advanced Features
                </p>
                <p className="text-sm text-muted-foreground/70 mt-2">AI voice assistant & more</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* --- SECTION 4: PROJECT JOURNEY (STAR METHOD) --- */}
      <section className="px-4 py-20">
        <div className="mx-auto max-w-4xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
              The Project Journey
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed">
              From challenge to solution: a comprehensive breakdown using the STAR methodology.
            </p>
          </div>
          <div className="relative pl-8 sm:pl-12">
            <div className="absolute left-[23px] sm:left-[27px] top-12 h-[calc(100%-3rem)] w-0.5 bg-gradient-to-b from-primary via-accent to-secondary rounded-full"></div>
            <div className="space-y-12 sm:space-y-16">
              {/* Situation */}
              <div className="relative">
                <div className="absolute -left-8 sm:-left-12 top-0 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary font-bold ring-4 ring-background border-2 border-primary/20">
                  S
                </div>
                <div className="bg-card/50 backdrop-blur-sm p-6 sm:p-8 rounded-xl border border-border/50">
                  <h3 className="text-xl sm:text-2xl font-semibold text-foreground mb-3">
                    Situation
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    The need for a portfolio that transcends a simple list of skills—one that serves
                    as a living proof-of-concept for advanced technical prowess, modern development
                    practices, and enterprise-grade architecture standards.
                  </p>
                </div>
              </div>

              {/* Task */}
              <div className="relative">
                <div className="absolute -left-8 sm:-left-12 top-0 flex h-12 w-12 items-center justify-center rounded-full bg-accent/10 text-accent font-bold ring-4 ring-background border-2 border-accent/20">
                  T
                </div>
                <div className="bg-card/50 backdrop-blur-sm p-6 sm:p-8 rounded-xl border border-border/50">
                  <h3 className="text-xl sm:text-2xl font-semibold text-foreground mb-3">Task</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    To architect and build a comprehensive portfolio from the ground up, showcasing
                    full-stack expertise through practical implementations of modern architecture,
                    stringent accessibility standards, and truly innovative user experiences.
                  </p>
                </div>
              </div>

              {/* Action */}
              <div className="relative">
                <div className="absolute -left-8 sm:-left-12 top-0 flex h-12 w-12 items-center justify-center rounded-full bg-secondary/10 text-secondary font-bold ring-4 ring-background border-2 border-secondary/20">
                  A
                </div>
                <div className="bg-card/50 backdrop-blur-sm p-6 sm:p-8 rounded-xl border border-border/50">
                  <h3 className="text-xl sm:text-2xl font-semibold text-foreground mb-3">Action</h3>
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    A feature-rich application was meticulously crafted using a comprehensive suite
                    of cutting-edge technologies and methodologies:
                  </p>
                  <div className="grid gap-4 sm:gap-3">
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <span className="text-muted-foreground">
                        Engineered with Next.js 15 & React 19 for unparalleled performance and
                        developer experience.
                      </span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <span className="text-muted-foreground">
                        Integrated dynamic blog via Medium RSS with intelligent caching and error
                        handling.
                      </span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <span className="text-muted-foreground">
                        Introduced Vapi AI voice assistant for hands-free navigation and
                        accessibility.
                      </span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <span className="text-muted-foreground">
                        Ensured WCAG 2.1 AA compliance and added global command menu (⌘K) for power
                        users.
                      </span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <span className="text-muted-foreground">
                        Implemented comprehensive TypeScript coverage and modern testing strategies.
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Result */}
              <div className="relative">
                <div className="absolute -left-8 sm:-left-12 top-0 flex h-12 w-12 items-center justify-center rounded-full bg-green-500/10 text-green-600 font-bold ring-4 ring-background border-2 border-green-500/20">
                  R
                </div>
                <div className="bg-card/50 backdrop-blur-sm p-6 sm:p-8 rounded-xl border border-border/50">
                  <h3 className="text-xl sm:text-2xl font-semibold text-foreground mb-3">Result</h3>
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    Successfully delivered a production-ready portfolio that demonstrates
                    enterprise-level development practices while achieving exceptional performance
                    metrics:
                  </p>
                  <div className="grid gap-4 sm:gap-3">
                    <div className="flex items-start gap-3">
                      <Target className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                      <span className="text-muted-foreground">
                        Achieved 95+ Lighthouse performance score with optimized loading and
                        rendering.
                      </span>
                    </div>
                    <div className="flex items-start gap-3">
                      <Target className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                      <span className="text-muted-foreground">
                        Implemented WCAG 2.1 AA accessibility standards, serving users with diverse
                        needs.
                      </span>
                    </div>
                    <div className="flex items-start gap-3">
                      <Target className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                      <span className="text-muted-foreground">
                        Delivered 15+ advanced features including AI integration and real-time blog
                        updates.
                      </span>
                    </div>
                    <div className="flex items-start gap-3">
                      <Target className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                      <span className="text-muted-foreground">
                        Created a maintainable, scalable codebase with comprehensive documentation.
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* --- SECTION 5: TECHNOLOGY STACK --- */}
      <section
        id="technology-stack"
        className="px-4 py-20 bg-gradient-to-br from-muted/30 to-muted/70"
      >
        <div className="mx-auto max-w-6xl">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-3xl sm:text-4xl font-bold text-foreground">
              The Tech Arsenal
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              A curated selection of modern technologies and powerful integrations that form the
              backbone of this portfolio application.
            </p>
          </div>

          <div className="space-y-16 lg:space-y-24">
            {/* Frontend Architecture */}
            <div className="grid items-center gap-8 lg:gap-12 lg:grid-cols-2">
              <div className="order-2 lg:order-1">
                <h3 className="mb-4 text-2xl sm:text-3xl font-bold text-foreground">
                  Frontend Excellence
                </h3>
                <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
                  Built with the latest Next.js 15 App Router and React 19, delivering blazing-fast
                  performance, rock-solid type safety, and an unparalleled developer experience.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full border border-primary/20">
                    Server Components
                  </span>
                  <span className="px-3 py-1 text-xs font-medium bg-accent/10 text-accent rounded-full border border-accent/20">
                    App Router
                  </span>
                  <span className="px-3 py-1 text-xs font-medium bg-secondary/10 text-secondary rounded-full border border-secondary/20">
                    Streaming SSR
                  </span>
                </div>
              </div>
              <div className="order-1 lg:order-2">
                <div className="rounded-xl bg-card/80 backdrop-blur-sm p-6 border border-border/50">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 rounded-lg bg-gradient-to-br from-blue-500/10 to-blue-600/10 border border-blue-500/20">
                      <Code className="h-8 w-8 text-blue-600 mb-2" />
                      <h4 className="font-semibold text-foreground text-sm mb-1">Next.js 15</h4>
                      <p className="text-xs text-muted-foreground">App Router & RSC</p>
                    </div>
                    <div className="p-4 rounded-lg bg-gradient-to-br from-cyan-500/10 to-cyan-600/10 border border-cyan-500/20">
                      <Code className="h-8 w-8 text-cyan-600 mb-2" />
                      <h4 className="font-semibold text-foreground text-sm mb-1">React 19</h4>
                      <p className="text-xs text-muted-foreground">Latest Features</p>
                    </div>
                    <div className="p-4 rounded-lg bg-gradient-to-br from-indigo-500/10 to-indigo-600/10 border border-indigo-500/20">
                      <Code className="h-8 w-8 text-indigo-600 mb-2" />
                      <h4 className="font-semibold text-foreground text-sm mb-1">TypeScript</h4>
                      <p className="text-xs text-muted-foreground">Full Type Safety</p>
                    </div>
                    <div className="p-4 rounded-lg bg-gradient-to-br from-purple-500/10 to-purple-600/10 border border-purple-500/20">
                      <Palette className="h-8 w-8 text-purple-600 mb-2" />
                      <h4 className="font-semibold text-foreground text-sm mb-1">Tailwind CSS</h4>
                      <p className="text-xs text-muted-foreground">Utility-First</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* API Integrations */}
            <div className="grid items-center gap-8 lg:gap-12 lg:grid-cols-2">
              <div className="order-2 lg:order-1">
                <div className="rounded-xl bg-card/80 backdrop-blur-sm p-6 border border-border/50">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 rounded-lg bg-gradient-to-br from-green-500/10 to-green-600/10 border border-green-500/20">
                      <Bot className="h-8 w-8 text-green-600 mb-2" />
                      <h4 className="font-semibold text-foreground text-sm mb-1">Vapi AI</h4>
                      <p className="text-xs text-muted-foreground">Voice Assistant</p>
                    </div>
                    <div className="p-4 rounded-lg bg-gradient-to-br from-orange-500/10 to-orange-600/10 border border-orange-500/20">
                      <Rss className="h-8 w-8 text-orange-600 mb-2" />
                      <h4 className="font-semibold text-foreground text-sm mb-1">Medium RSS</h4>
                      <p className="text-xs text-muted-foreground">Dynamic Blog</p>
                    </div>
                    <div className="p-4 rounded-lg bg-gradient-to-br from-red-500/10 to-red-600/10 border border-red-500/20">
                      <Mail className="h-8 w-8 text-red-600 mb-2" />
                      <h4 className="font-semibold text-foreground text-sm mb-1">Resend</h4>
                      <p className="text-xs text-muted-foreground">Email Service</p>
                    </div>
                    <div className="p-4 rounded-lg bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 border border-yellow-500/20">
                      <Database className="h-8 w-8 text-yellow-600 mb-2" />
                      <h4 className="font-semibold text-foreground text-sm mb-1">Airtable</h4>
                      <p className="text-xs text-muted-foreground">Data Management</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="order-1 lg:order-2">
                <h3 className="mb-4 text-2xl sm:text-3xl font-bold text-foreground">
                  Powerful Integrations
                </h3>
                <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
                  Leveraging external services to deliver dynamic content, seamless communication,
                  and intelligent AI-powered features that enhance the user experience.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 text-xs font-medium bg-green-500/10 text-green-600 rounded-full border border-green-500/20">
                    AI Integration
                  </span>
                  <span className="px-3 py-1 text-xs font-medium bg-orange-500/10 text-orange-600 rounded-full border border-orange-500/20">
                    Content API
                  </span>
                  <span className="px-3 py-1 text-xs font-medium bg-red-500/10 text-red-600 rounded-full border border-red-500/20">
                    Email API
                  </span>
                </div>
              </div>
            </div>

            {/* Development & Deployment */}
            <div className="grid items-center gap-8 lg:gap-12 lg:grid-cols-2">
              <div className="order-2 lg:order-1">
                <h3 className="mb-4 text-2xl sm:text-3xl font-bold text-foreground">
                  Modern Development
                </h3>
                <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
                  Comprehensive tooling and deployment pipeline ensuring code quality, performance
                  optimization, and seamless continuous deployment.
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 text-xs font-medium bg-blue-500/10 text-blue-600 rounded-full border border-blue-500/20">
                    ESLint & Prettier
                  </span>
                  <span className="px-3 py-1 text-xs font-medium bg-purple-500/10 text-purple-600 rounded-full border border-purple-500/20">
                    Husky Hooks
                  </span>
                  <span className="px-3 py-1 text-xs font-medium bg-pink-500/10 text-pink-600 rounded-full border border-pink-500/20">
                    Vercel Deploy
                  </span>
                </div>
              </div>
              <div className="order-1 lg:order-2">
                <div className="rounded-xl bg-card/80 backdrop-blur-sm p-6 border border-border/50">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 rounded-lg bg-gradient-to-br from-slate-500/10 to-slate-600/10 border border-slate-500/20">
                      <Code className="h-8 w-8 text-slate-600 mb-2" />
                      <h4 className="font-semibold text-foreground text-sm mb-1">ESLint</h4>
                      <p className="text-xs text-muted-foreground">Code Quality</p>
                    </div>
                    <div className="p-4 rounded-lg bg-gradient-to-br from-pink-500/10 to-pink-600/10 border border-pink-500/20">
                      <Code className="h-8 w-8 text-pink-600 mb-2" />
                      <h4 className="font-semibold text-foreground text-sm mb-1">Prettier</h4>
                      <p className="text-xs text-muted-foreground">Code Format</p>
                    </div>
                    <div className="p-4 rounded-lg bg-gradient-to-br from-emerald-500/10 to-emerald-600/10 border border-emerald-500/20">
                      <Globe className="h-8 w-8 text-emerald-600 mb-2" />
                      <h4 className="font-semibold text-foreground text-sm mb-1">Vercel</h4>
                      <p className="text-xs text-muted-foreground">Deployment</p>
                    </div>
                    <div className="p-4 rounded-lg bg-gradient-to-br from-gray-500/10 to-gray-600/10 border border-gray-500/20">
                      <Shield className="h-8 w-8 text-gray-600 mb-2" />
                      <h4 className="font-semibold text-foreground text-sm mb-1">Husky</h4>
                      <p className="text-xs text-muted-foreground">Git Hooks</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* --- SECTION 6: VISUAL GALLERY --- */}
      <section className="px-4 py-20">
        <div className="mx-auto max-w-6xl">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-4xl font-bold text-foreground">Visual Gallery</h2>
            <p className="text-lg text-muted-foreground">
              A glimpse into the design and user interface of the HyperFox Portfolio.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* Replace these divs with your actual <Image> components */}
            <div className="aspect-[4/3] rounded-lg bg-card border border-border hover:scale-105 transition-transform duration-300">
              {/* <Image src="/path/to/image1.png" alt="Screenshot 1" layout="fill" objectFit="cover" /> */}
            </div>
            <div className="aspect-[4/3] rounded-lg bg-card border border-border hover:scale-105 transition-transform duration-300 col-span-2">
              {/* <Image src="/path/to/image2.png" alt="Screenshot 2" layout="fill" objectFit="cover" /> */}
            </div>
            <div className="aspect-[4/3] rounded-lg bg-card border border-border hover:scale-105 transition-transform duration-300">
              {/* <Image src="/path/to/image3.png" alt="Screenshot 3" layout="fill" objectFit="cover" /> */}
            </div>
          </div>
        </div>
      </section>

      {/* --- SECTION 7: CTA & FOOTER --- */}
      <section className="px-4 py-20 text-center border-t border-border">
        <div className="mx-auto max-w-4xl">
          <h2 className="mb-6 text-4xl font-bold text-foreground md:text-5xl">Ready to Explore?</h2>
          <p className="mb-8 text-lg text-muted-foreground">
            Experience the live application and its host of features, or dive deep into the source
            code to uncover the architectural patterns and implementation details.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="inline-flex h-12 items-center justify-center rounded-md bg-primary px-8 text-base font-medium text-primary-foreground transition-colors hover:bg-primary/90 group"
            >
              <span>Visit Live Portfolio</span>
              <ExternalLink className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Link>
            <Link
              href="https://github.com/thanmaisai/hyperfox-portfolio"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex h-12 items-center justify-center rounded-md border border-border bg-card px-8 text-base font-medium text-card-foreground transition-colors hover:bg-accent hover:text-accent-foreground group"
            >
              <Github className="mr-2 h-4 w-4" />
              <span>View Source Code</span>
            </Link>
          </div>

          {/* Project Navigation */}
          <div className="mt-20 pt-8 border-t border-border/50 flex flex-col sm:flex-row items-center justify-between gap-4">
            <Link
              href="/projects"
              className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors group"
            >
              <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
              <span>Back to All Projects</span>
            </Link>

            <div className="flex items-center gap-4">
              <Link
                href="/projects/lor-system"
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                ← Previous: LOR System
              </Link>
              <div className="w-px h-4 bg-border"></div>
              <Link
                href="/projects/hyperbooks"
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                Next: HyperBooks →
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
