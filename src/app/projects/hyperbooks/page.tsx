import Image from "next/image";
import Link from "next/link";

export default function HyperBooksPage() {
  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h1 className="text-3xl font-bold tracking-tight sm:text-4xl">HyperBooks</h1>
        <p className="text-[hsl(var(--muted-foreground))]">An AI-powered book recommendation and summary platform.</p>
      </div>

      <div className="relative aspect-video w-full max-w-3xl rounded-lg overflow-hidden">
        <Image
          src="/projects_images/hyperbooks.png"
          alt="HyperBooks"
          fill
          className="object-cover"
        />
      </div>

      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Overview</h2>
        <p>
          HyperBooks is an AI-powered platform that helps users discover new books based on their 
          preferences and provides concise summaries to help them decide what to read next. The platform 
          uses natural language processing to understand user preferences and generate personalized 
          recommendations.
        </p>
      </div>

      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Features</h2>
        <ul className="list-disc pl-5 space-y-2">
          <li>
            <strong>Personalized Recommendations</strong>: Uses AI to analyze reading preferences and 
            suggest books that match the user's interests.
          </li>
          <li>
            <strong>AI-Generated Summaries</strong>: Provides concise, informative summaries of books 
            to help users decide if they want to read them.
          </li>
          <li>
            <strong>Reading Lists</strong>: Allows users to create and manage reading lists for future reference.
          </li>
          <li>
            <strong>Social Sharing</strong>: Enables users to share their favorite books and recommendations 
            with friends on social media.
          </li>
        </ul>
      </div>

      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Technologies Used</h2>
        <div className="flex flex-wrap gap-2">
          <span className="rounded-full bg-[hsl(var(--secondary))] px-3 py-1 text-sm text-[hsl(var(--secondary-foreground))]">
            Next.js
          </span>
          <span className="rounded-full bg-[hsl(var(--secondary))] px-3 py-1 text-sm text-[hsl(var(--secondary-foreground))]">
            OpenAI
          </span>
          <span className="rounded-full bg-[hsl(var(--secondary))] px-3 py-1 text-sm text-[hsl(var(--secondary-foreground))]">
            MongoDB
          </span>
          <span className="rounded-full bg-[hsl(var(--secondary))] px-3 py-1 text-sm text-[hsl(var(--secondary-foreground))]">
            Tailwind CSS
          </span>
        </div>
      </div>

      <div className="flex justify-start">
        <Link
          href="/projects"
          className="inline-flex items-center rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--card))] px-4 py-2 text-sm font-medium transition-colors hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))]"
        >
          ← Back to Projects
        </Link>
      </div>
    </div>
  );
}
