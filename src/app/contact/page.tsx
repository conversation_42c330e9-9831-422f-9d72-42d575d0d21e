"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>ert<PERSON>ircle, CheckCircle, Heart } from "lucide-react";
import { sendEmail, isEmailConfigured } from "@/lib/email";

interface FormData {
  name: string;
  email: string;
  message: string;
  subject: string;
  company: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  message?: string;
}

export default function ContactPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    message: "",
    subject: "",
    company: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<null | "success" | "error">(null);



  const validateField = (name: string, value: string): string | undefined => {
    switch (name) {
      case "name":
        if (!value.trim()) return "Name is required";
        if (value.length < 2) return "Name must be at least 2 characters";
        if (value.length > 100) return "Name must be less than 100 characters";
        break;
      case "email":
        if (!value.trim()) return "Email is required";
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) return "Please enter a valid email address";
        break;
      case "message":
        if (!value.trim()) return "Message is required";
        if (value.length < 10) return "Message must be at least 10 characters";
        if (value.length > 2000) return "Message must be less than 2000 characters";
        break;
      case "subject":
        if (value && value.length > 200) return "Subject must be less than 200 characters";
        break;
    }
    return undefined;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);

    // Validate all fields
    const newErrors: FormErrors = {};
    Object.entries(formData).forEach(([key, value]) => {
      if (key !== "company") { // company is optional
        const error = validateField(key, value);
        if (error) newErrors[key as keyof FormErrors] = error;
      }
    });

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setIsSubmitting(false);
      return;
    }

    try {
      // Check if EmailJS is configured
      if (!isEmailConfigured()) {
        // Fallback to API route
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });

        const result = await response.json();

        if (result.success) {
          setSubmitStatus("success");
          setFormData({ name: "", email: "", message: "", subject: "", company: "" });
          setErrors({});

          setTimeout(() => {
            router.push('/');
          }, 3000);
        } else {
          throw new Error(result.error || 'Failed to send message');
        }
      } else {
        const result = await sendEmail(formData);

        if (result.success) {
          setSubmitStatus("success");
          setFormData({ name: "", email: "", message: "", subject: "", company: "" });
          setErrors({});

          setTimeout(() => {
            router.push('/');
          }, 3000);
        } else {
          throw new Error(result.error || 'Failed to send message');
        }
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus("error");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <style jsx>{`
        @keyframes progress {
          from { width: 0%; }
          to { width: 100%; }
        }
        @keyframes fadeInScale {
          from {
            opacity: 0;
            transform: scale(0.9) translateY(20px);
          }
          to {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
        .success-modal {
          animation: fadeInScale 0.5s ease-out forwards;
        }
      `}</style>

      <div className="max-w-4xl mx-auto py-8 px-4 space-y-12">
        <header className="text-center space-y-4">
          <h1 className="text-4xl font-bold">Contact</h1>
          <p className="text-lg text-[hsl(var(--muted-foreground))] max-w-2xl mx-auto">
            Get in touch for collaboration or opportunities
          </p>
        </header>

      <div className="max-w-2xl mx-auto">
        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div className="space-y-3">
              <label htmlFor="name" className="text-sm font-medium">
                Name
              </label>
              <input
                id="name"
                name="name"
                type="text"
                required
                value={formData.name}
                onChange={handleChange}
                className="w-full rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--input))] px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:border-transparent"
                placeholder="Your name"
              />
              {errors.name && (
                <p className="text-red-500 text-xs flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {errors.name}
                </p>
              )}
            </div>

            <div className="space-y-3">
              <label htmlFor="email" className="text-sm font-medium">
                Email
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="w-full rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--input))] px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:border-transparent"
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="text-red-500 text-xs flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {errors.email}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-3">
            <label htmlFor="subject" className="text-sm font-medium">
              Subject
            </label>
            <input
              id="subject"
              name="subject"
              type="text"
              value={formData.subject}
              onChange={handleChange}
              className="w-full rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--input))] px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:border-transparent"
              placeholder="What's this about?"
            />
          </div>

          <div className="space-y-3">
            <label htmlFor="company" className="text-sm font-medium">
              Company
            </label>
            <input
              id="company"
              name="company"
              type="text"
              value={formData.company}
              onChange={handleChange}
              className="w-full rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--input))] px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:border-transparent"
              placeholder="Your company (optional)"
            />
          </div>

          <div className="space-y-3">
            <label htmlFor="message" className="text-sm font-medium">
              Message
            </label>
            <textarea
              id="message"
              name="message"
              required
              value={formData.message}
              onChange={handleChange}
              rows={6}
              className="w-full rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--input))] px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:border-transparent resize-none"
              placeholder="Tell me about your project or how I can help..."
            />
            {errors.message && (
              <p className="text-red-500 text-xs flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                {errors.message}
              </p>
            )}
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] px-6 py-4 rounded-md font-medium hover:bg-[hsl(var(--primary))]/90 focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-base"
          >
            {isSubmitting ? "Sending..." : "Send Message"}
          </button>

          {submitStatus === "success" && (
            <div className="fixed inset-0 bg-[hsl(var(--background))]/80 backdrop-blur-sm flex items-center justify-center z-50">
              <div className="success-modal bg-[hsl(var(--card))] border border-[hsl(var(--border))] rounded-2xl p-8 max-w-md mx-4 text-center shadow-lg">
                <div className="mb-6">
                  <div className="w-16 h-16 bg-[hsl(var(--secondary))] rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-[hsl(var(--primary))]" />
                  </div>
                  <h3 className="text-2xl font-bold text-[hsl(var(--foreground))] mb-2">Thank You!</h3>
                  <p className="text-[hsl(var(--muted-foreground))] leading-relaxed">
                    Your message has been sent successfully. I appreciate you reaching out and will get back to you soon.
                  </p>
                </div>

                <div className="flex items-center justify-center gap-2 text-sm text-[hsl(var(--muted-foreground))] mb-4">
                  <Heart className="w-4 h-4 text-[hsl(var(--primary))]" />
                  <span>Thanks for connecting</span>
                </div>

                <div className="text-xs text-[hsl(var(--muted-foreground))]/70">
                  Redirecting to home page in a moment...
                </div>

                {/* Subtle loading animation */}
                <div className="mt-4 w-full bg-[hsl(var(--secondary))] rounded-full h-1">
                  <div className="bg-[hsl(var(--primary))] h-1 rounded-full" style={{ animation: 'progress 3s linear forwards' }}></div>
                </div>
              </div>
            </div>
          )}

          {submitStatus === "error" && (
            <div className="text-center p-6 bg-[hsl(var(--destructive))]/10 border border-[hsl(var(--destructive))]/20 rounded-lg text-[hsl(var(--destructive))]">
              <p className="font-medium text-base">Failed to send message</p>
              <p className="text-sm mt-1">Please try again or email me <NAME_EMAIL></p>
            </div>
          )}
        </form>
      </div>
      </div>
    </>
  );
}
