import { ImageModal } from '@/components/ImageModal';
import { getUpdates } from '@/lib/airtable';
import { Award, Calendar, ExternalLink } from 'lucide-react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Updates - Thanmai Sai',
  description: 'Latest updates and activities from Thanmai Sai',
};

const formatDate = (dateString: string) => {
  if (!dateString) return 'No date';

  let date;

  // Handle different date formats
  if (dateString.includes('/')) {
    // DD/MM/YYYY format
    const [day, month, year] = dateString.split('/');
    date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
  } else {
    // ISO format (YYYY-MM-DD) or other standard formats
    date = new Date(dateString);
  }

  // Check if date is valid
  if (isNaN(date.getTime())) {
    return dateString; // Return original string if parsing fails
  }

  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

export default async function FeedPage() {
  const updates = await getUpdates();

  return (
    <div className="max-w-2xl -mx-4 sm:-mx-6 lg:-mx-10">
      <div className="pl-4 sm:pl-6 lg:pl-10 transition-colors duration-200">
        <header className="mb-4">
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-[hsl(var(--foreground))]">
            Updates
          </h1>
          <p className="text-base text-[hsl(var(--muted-foreground))] max-w-[700px]">
            Discover, and stay updated with my latest activities.
          </p>
        </header>

        <div className="space-y-6">
          {updates.length === 0 ? (
            <div className="text-left py-3 px-4 rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--card))]">
              <p className="text-sm text-[hsl(var(--muted-foreground))]">No updates available</p>
            </div>
          ) : (
            updates.map(update => (
              <div
                key={update.id}
                className="group border-b border-[hsl(var(--border))] pb-6 last:border-0 transition-colors duration-200 hover:bg-[hsl(var(--card))]/30 rounded-md p-3 -mx-3"
              >
                <div className="flex flex-wrap gap-x-2 gap-y-0.5 items-center mb-1.5 text-xs text-[hsl(var(--muted-foreground))]">
                  <time
                    dateTime={update.date}
                    className="flex items-center gap-0.5 px-2 py-0.5 rounded-full bg-[hsl(var(--muted))]/50"
                  >
                    <Calendar className="h-3 w-3" />
                    {formatDate(update.date)}
                  </time>

                  {update.type && (
                    <span className="capitalize px-2 py-0.5 rounded-full bg-[hsl(var(--secondary))]/50">
                      {update.type}
                    </span>
                  )}

                  {update.achievement && (
                    <span className="flex items-center gap-0.5 px-2 py-0.5 rounded-full bg-[hsl(var(--primary))]/10 text-[hsl(var(--primary))]">
                      <Award className="h-3 w-3" />
                      <span>Achievement</span>
                    </span>
                  )}
                </div>
                {update.title && (
                  <h3 className="text-base font-medium mb-1.5 mt-1.5 tracking-tight text-[hsl(var(--foreground))] group-hover:text-[hsl(var(--primary))] transition-colors duration-200">
                    {update.title}
                  </h3>
                )}
                <p className="text-sm text-[hsl(var(--muted-foreground))] mb-2 leading-normal">
                  {update.content}
                </p>
                <div className="flex flex-wrap items-start gap-2">
                  {update.images && update.images.length > 0 && (
                    <div className="mt-1 rounded-md overflow-hidden ring-3 ring-[hsl(var(--border))] hover:ring-[hsl(var(--primary))] transition-colors duration-200">
                      <ImageModal src={update.images} alt={update.title || 'Update image'} />
                    </div>
                  )}

                  {update.link && (
                    <a
                      href={update.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-0.5 text-xs text-[hsl(var(--muted-foreground))] hover:text-[hsl(var(--primary))] transition-colors duration-200 group mt-1"
                    >
                      <ExternalLink className="h-3 w-3 group-hover:text-[hsl(var(--primary))] transition-colors duration-200" />
                      <span className="opacity-0 max-w-0 group-hover:max-w-[40px] group-hover:opacity-100 transition-all duration-300 overflow-hidden">
                        View
                      </span>
                    </a>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
