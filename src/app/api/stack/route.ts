import { fetchTechStackData } from '@/lib/airtable/techStackService';
import { NextResponse } from 'next/server';

/**
 * API route handler for fetching tech stack data
 * Includes caching at the service level
 */
export async function GET() {
  try {
    const techStackData = await fetchTechStackData();
    return NextResponse.json({ techStackData }, { 
      status: 200,
      headers: {
        // Cache for 5 minutes in the browser
        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=60'
      }
    });
  } catch (error) {
    console.error('Error fetching tech stack data:', error);
    return NextResponse.json({ error: 'Failed to fetch tech stack data' }, { status: 500 });
  }
}
