/**
 * Resume Download API Route
 *
 * Handles PDF resume downloads with proper headers and error handling.
 * Includes rate limiting to prevent abuse.
 */

import { NextRequest, NextResponse } from 'next/server';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import {
  createErrorResponse,
  createNotFoundResponse
} from '@/lib/api/response';
import {
  getRequestContext,
  checkRateLimit,
  RATE_LIMIT_CONFIGS,
  logRequest
} from '@/lib/api/middleware';
import { ApiErrorCode } from '@/lib/api/types';

export async function GET(request: NextRequest) {
  const context = getRequestContext(request);
  logRequest(request, context, { endpoint: 'resume-download' });

  try {
    // Apply rate limiting
    const { isLimited } = checkRateLimit(request, RATE_LIMIT_CONFIGS.moderate);
    if (isLimited) {
      return createErrorResponse({
        code: ApiErrorCode.RATE_LIMITED,
        message: 'Too many download requests. Please try again later.',
        statusCode: 429,
      });
    }

    // Check if file exists
    const filePath = join(process.cwd(), 'public', 'Thanmai_Resume.pdf');
    if (!existsSync(filePath)) {
      return createNotFoundResponse('Resume PDF');
    }

    // Read the PDF file
    const fileBuffer = readFileSync(filePath);

    // Create response with proper headers for download
    const response = new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="Thanmai_Sai_Resume.pdf"',
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'Last-Modified': new Date().toUTCString(),
      },
    });

    return response;
  } catch (error) {
    console.error('Resume download error:', error);

    return createErrorResponse({
      code: ApiErrorCode.INTERNAL_ERROR,
      message: 'Failed to download resume',
      statusCode: 500,
      details: process.env.NODE_ENV === 'development' ? error : undefined,
    });
  }
}
