/**
 * Contact API Route
 *
 * Handles contact form submissions with validation, rate limiting,
 * and standardized response format.
 */

import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
  createValidationErrorResponse,
  createRateLimitResponse,
  createOptionsResponse
} from '@/lib/api/response';
import {
  getRequestContext,
  checkRateLimit,
  RATE_LIMIT_CONFIGS,
  logRequest,
  parseJsonBody
} from '@/lib/api/middleware';
import {
  validateContactForm,
  sanitizeContactForm
} from '@/lib/api/validation';
import { ApiErrorCode } from '@/lib/api/types';

export async function POST(request: NextRequest) {
  const context = getRequestContext(request);
  logRequest(request, context, { endpoint: 'contact' });

  try {
    // Apply rate limiting (strict for contact forms)
    const { isLimited, info } = checkRateLimit(request, RATE_LIMIT_CONFIGS.strict);
    if (isLimited) {
      return createRateLimitResponse(info.resetTime);
    }

    // Parse request body
    const body = await parseJsonBody<Record<string, unknown>>(request);
    if (!body) {
      return createErrorResponse({
        code: ApiErrorCode.VALIDATION_ERROR,
        message: 'Invalid JSON in request body',
        statusCode: 400,
      });
    }

    // Sanitize inputs
    const formData = sanitizeContactForm(body);

    // Validate form data
    const validation = validateContactForm(formData);
    if (!validation.isValid) {
      return createValidationErrorResponse(validation.errors);
    }

    // TODO: Implement actual email sending
    // For now, simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return createSuccessResponse(
      { submitted: true },
      {
        message: 'Message sent successfully! I\'ll get back to you soon.',
        meta: {
          submittedAt: new Date().toISOString(),
          requestId: context.requestId,
        },
      }
    );

  } catch (error) {
    console.error('Contact form error:', error);

    return createErrorResponse({
      code: ApiErrorCode.INTERNAL_ERROR,
      message: 'Internal server error. Please try again later.',
      statusCode: 500,
      details: process.env.NODE_ENV === 'development' ? error : undefined,
    });
  }
}

export async function OPTIONS() {
  return createOptionsResponse(['POST', 'OPTIONS']);
}
