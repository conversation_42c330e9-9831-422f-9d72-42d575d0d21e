/**
 * Blog API Route
 *
 * Handles fetching all blog posts from various sources (Medium, local, etc.)
 * with standardized response format and error handling.
 */

import { NextRequest } from 'next/server';
import { getAllBlogPosts } from '@/lib/api/medium';
import {
  createSuccessResponse,
  createErrorResponse,
  createOptionsResponse
} from '@/lib/api/response';
import {
  getRequestContext,
  checkRateLimit,
  RATE_LIMIT_CONFIGS,
  logRequest
} from '@/lib/api/middleware';
import { ApiErrorCode } from '@/lib/api/types';

export async function GET(request: NextRequest) {
  const context = getRequestContext(request);
  logRequest(request, context);

  try {
    // Apply rate limiting
    const { isLimited } = checkRateLimit(request, RATE_LIMIT_CONFIGS.moderate);
    if (isLimited) {
      return createErrorResponse({
        code: ApiErrorCode.RATE_LIMITED,
        message: 'Too many requests. Please try again later.',
        statusCode: 429,
      });
    }

    const posts = await getAllBlogPosts();

    return createSuccessResponse(posts, {
      meta: {
        count: posts.length,
        lastUpdated: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Blog API error:', error);

    return createErrorResponse({
      code: ApiErrorCode.EXTERNAL_SERVICE_ERROR,
      message: 'Failed to fetch blog posts',
      statusCode: 500,
      details: process.env.NODE_ENV === 'development' ? error : undefined,
    });
  }
}

export async function OPTIONS() {
  return createOptionsResponse(['GET', 'OPTIONS']);
}
