/**
 * Medium Blog API Route
 *
 * Handles fetching blog posts specifically from Medium with caching,
 * fallback mechanisms, and standardized response format.
 */

import { NextRequest } from 'next/server';
import { fetchMediumBlogPosts } from '@/lib/api/medium';
import {
  createSuccessResponse,
  createErrorResponse,
  createOptionsResponse
} from '@/lib/api/response';
import {
  getRequestContext,
  checkRateLimit,
  RATE_LIMIT_CONFIGS,
  logRequest
} from '@/lib/api/middleware';
import { ApiErrorCode } from '@/lib/api/types';

export async function GET(request: NextRequest) {
  const context = getRequestContext(request);
  logRequest(request, context, { source: 'medium' });

  try {
    // Apply rate limiting
    const { isLimited } = checkRateLimit(request, RATE_LIMIT_CONFIGS.moderate);
    if (isLimited) {
      return createErrorResponse({
        code: ApiErrorCode.RATE_LIMITED,
        message: 'Too many requests. Please try again later.',
        statusCode: 429,
      });
    }

    const blogPosts = await fetchMediumBlogPosts();

    return createSuccessResponse(blogPosts, {
      meta: {
        count: blogPosts.length,
        lastUpdated: new Date().toISOString(),
        source: 'medium',
      },
    });
  } catch (error) {
    console.error('Medium API error:', error);

    return createErrorResponse({
      code: ApiErrorCode.EXTERNAL_SERVICE_ERROR,
      message: 'Failed to fetch Medium blog posts',
      statusCode: 500,
      details: process.env.NODE_ENV === 'development' ? error : undefined,
    });
  }
}

export async function OPTIONS() {
  return createOptionsResponse(['GET', 'OPTIONS']);
}
