'use client';

import { useTechStackData } from '@/hooks';
import { Tool, TechCategory } from '@/types/techStack';
import {
  AlertCircle,
  ExternalLink,
  LucideIcon,
  Monitor,
  Search,
  Server,
  Settings,
  Smartphone,
} from 'lucide-react';
import { useState } from 'react';

// Map string icon names from Airtable to Lucide components
const iconMap: Record<string, LucideIcon> = {
  Monitor: Monitor,
  Server: Server,
  Settings: Settings,
  Smartphone: Smartphone,
  AlertCircle: AlertCircle
};

// Professional Tech Card Component
interface TechCardProps {
  tool: Tool;
}

function TechCard({ tool }: TechCardProps) {
  const getLevelBadgeStyle = (level: string) => {
    switch (level) {
      case 'Expert':
        return 'bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))]';
      case 'Advanced':
        return 'bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))]';
      case 'Intermediate':
        return 'bg-[hsl(var(--muted))] text-[hsl(var(--muted-foreground))]';
      default:
        return 'bg-[hsl(var(--muted))] text-[hsl(var(--muted-foreground))]';
    }
  };

  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case 'Core Skill':
        return 'bg-[hsl(var(--accent))] text-[hsl(var(--accent-foreground))]';
      case 'Learning':
        return 'bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))]';
      default:
        return 'bg-[hsl(var(--muted))] text-[hsl(var(--muted-foreground))]';
    }
  };

  return (
    <div className="group cursor-pointer" onClick={() => window.open(tool.website, '_blank')}>
      <div className="relative rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] p-5 transition-colors duration-200 hover:border-[hsl(var(--muted-foreground))] h-full">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="font-semibold text-lg text-[hsl(var(--foreground))] group-hover:text-[hsl(var(--primary))] transition-colors">
                {tool.name}
              </h3>
              <p className="text-sm text-[hsl(var(--muted-foreground))] mt-1 line-clamp-2">
                {tool.description}
              </p>
            </div>
            <ExternalLink className="h-4 w-4 text-[hsl(var(--muted-foreground))] group-hover:text-[hsl(var(--primary))] transition-colors flex-shrink-0 ml-2" />
          </div>

          {/* Level and Status Badges - Using consistent design system */}
          <div className="flex flex-wrap gap-2">
            <span
              className={`px-2 py-1 rounded text-xs font-medium ${getLevelBadgeStyle(tool.level)}`}
            >
              {tool.level}
            </span>
            <span
              className={`px-2 py-1 rounded text-xs font-medium ${getStatusBadgeStyle(tool.status)}`}
            >
              {tool.status}
            </span>
          </div>

          {/* Tags - Matching project card style */}
          <div className="flex flex-wrap gap-2">
            {tool.tags.map((tag: string, tagIndex: number) => (
              <span
                key={tagIndex}
                className="bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))] px-2 py-1 text-xs rounded"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function StackPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('All');
  const [selectedStatus, setSelectedStatus] = useState('All');

  // Get tech stack data from API via custom hook
  const { techStackData, isLoading, error } = useTechStackData();

  // Filter tools based on search term, level, and status
  const filteredTools = techStackData
    .map(category => {
      // Convert string icon to actual component if needed
      const categoryWithIcon = {
        ...category,
        icon:
          typeof category.icon === 'string'
            ? iconMap[category.icon as string] || Monitor
            : category.icon,
      };

      return {
        ...categoryWithIcon,
        tools: category.tools.filter((tool: Tool) => {
          const matchesSearch =
            tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            tool.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
            tool.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm.toLowerCase()));

          const matchesLevel = selectedLevel === 'All' || tool.level === selectedLevel;
          const matchesStatus = selectedStatus === 'All' || tool.status === selectedStatus;

          return matchesSearch && matchesLevel && matchesStatus;
        }),
      };
    })
    .filter(category => category.tools.length > 0);

  const allLevels = ['All', 'Expert', 'Advanced', 'Intermediate'];
  const allStatuses = ['All', 'Core Skill', 'Learning'];

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-16 space-y-4">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
        <p className="text-lg text-[hsl(var(--muted-foreground))]">Loading tech stack data...</p>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-16 space-y-4 max-w-2xl mx-auto">
        <div className="p-4 flex items-center justify-center bg-red-100 dark:bg-red-900/30 rounded-full">
          <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
        </div>
        <h2 className="text-2xl font-bold text-center">Unable to load tech stack data</h2>
        <p className="text-center text-[hsl(var(--muted-foreground))]">
          There was an error loading the tech stack data. Please try refreshing the page or contact
          support if the problem persists.
        </p>
        <button 
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-md hover:bg-[hsl(var(--primary))/90] transition-colors"
        >
          Refresh Page
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <header className="space-y-6">
        <div className="space-y-4">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl text-[hsl(var(--foreground))]">
            Tech Stack
          </h1>
          <p className="text-[hsl(var(--muted-foreground))] text-lg max-w-2xl">
            A comprehensive overview of the technologies, tools, and frameworks I use to build
            amazing digital experiences.
          </p>
        </div>

        {/* Simple stats */}
        <div className="flex items-center gap-8 text-sm text-[hsl(var(--muted-foreground))]">
          <span>
            {techStackData.reduce((acc: number, cat: any) => acc + cat.tools.length, 0)} Technologies
          </span>
          <span>{techStackData.length} Categories</span>
        </div>
      </header>

      {/* Search and Filters */}
      <div className="space-y-4">
        {/* Search Input */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="h-4 w-4 text-[hsl(var(--muted-foreground))]" />
          </div>
          <input
            type="text"
            placeholder="Search technologies..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--input))] text-[hsl(var(--foreground))] placeholder-[hsl(var(--muted-foreground))] focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:border-transparent transition-all duration-200"
          />
        </div>

        {/* Filter Buttons - Consistent with design system */}
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-[hsl(var(--foreground))]">Level:</span>
            <div className="flex gap-1">
              {allLevels.map(level => (
                <button
                  key={level}
                  onClick={() => setSelectedLevel(level)}
                  className={`px-3 py-1 rounded text-xs font-medium transition-all duration-200 ${
                    selectedLevel === level
                      ? 'bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))]'
                      : 'bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))] hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))]'
                  }`}
                >
                  {level}
                </button>
              ))}
            </div>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-[hsl(var(--foreground))]">Status:</span>
            <div className="flex gap-1 flex-wrap">
              {allStatuses.map(status => (
                <button
                  key={status}
                  onClick={() => setSelectedStatus(status)}
                  className={`px-3 py-1 rounded text-xs font-medium transition-all duration-200 ${
                    selectedStatus === status
                      ? 'bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))]'
                      : 'bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))] hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))]'
                  }`}
                >
                  {status}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Tool Categories */}
      <div className="space-y-12">
        {filteredTools.map((category: TechCategory) => {
          const CategoryIcon = category.icon;

          return (
            <section key={category.category} className="space-y-6">
              {/* Minimal Category Header */}
              <div className="flex items-center gap-4 pb-4 border-b border-[hsl(var(--border))]">
                <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-[hsl(var(--muted))]">
                  <CategoryIcon className="h-5 w-5 text-[hsl(var(--muted-foreground))]" />
                </div>
                <div className="flex-1">
                  <h2 className="text-2xl font-semibold text-[hsl(var(--foreground))]">
                    {category.category}
                  </h2>
                  <p className="text-[hsl(var(--muted-foreground))] text-sm">
                    {category.description}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-[hsl(var(--foreground))]">
                    {category.tools.length}
                  </div>
                  <div className="text-xs text-[hsl(var(--muted-foreground))]">
                    {category.tools.length === 1 ? 'Tool' : 'Tools'}
                  </div>
                </div>
              </div>

              {/* Tools Grid */}
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {category.tools.map((tool: Tool) => (
                  <TechCard key={tool.name} tool={tool} />
                ))}
              </div>
            </section>
          );
        })}

        {filteredTools.length === 0 && (
          <div className="flex items-center justify-center py-16">
            <p className="text-lg text-[hsl(var(--muted-foreground))]">
              No technologies found matching your search.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
