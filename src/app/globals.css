@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure proper scrolling behavior */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
  height: 100%;
}

body {
  overflow-x: hidden;
  height: 100%;
  margin: 0;
  padding: 0;
}

/* Fix for potential scrolling issues */
#__next,
#root {
  height: 100%;
}

/* Ensure main content area is scrollable */
main {
  scroll-behavior: smooth;
}

/* Full height layout utilities */
.full-height-layout {
  height: 100vh;
  overflow: hidden;
}

.scrollable-content {
  overflow-y: auto;
  height: 100%;
}

/* Base theme variables */
:root {
  --radius: 0.5rem;
  --transition-duration: 0.4s;
}

/* Dark theme (default) */
.theme-dark, :root {
  --background: 220 10% 8%;
  --foreground: 220 10% 90%;
  --card: 220 10% 12%;
  --card-foreground: 220 10% 90%;
  --border: 220 10% 18%;
  --input: 220 10% 12%;
  --primary: 220 10% 90%;
  --primary-foreground: 220 10% 8%;
  --secondary: 220 10% 16%;
  --secondary-foreground: 220 10% 90%;
  --accent: 220 10% 25%;
  --accent-foreground: 220 10% 90%;
  --destructive: 0 70% 50%;
  --destructive-foreground: 220 10% 90%;
  --ring: 220 10% 25%;
  --muted: 220 10% 16%;
  --muted-foreground: 220 10% 65%;
  --popover: 220 10% 12%;
  --popover-foreground: 220 10% 90%;
}

/* Synthwave theme */
.theme-synthwave {
  --background: 260 15% 12%;
  --foreground: 320 50% 90%;
  --card: 260 15% 16%;
  --card-foreground: 320 50% 90%;
  --border: 280 40% 35%;
  --input: 260 15% 16%;
  --primary: 320 70% 60%;
  --primary-foreground: 260 15% 12%;
  --secondary: 260 40% 40%;
  --secondary-foreground: 320 50% 90%;
  --accent: 280 50% 45%;
  --accent-foreground: 320 50% 90%;
  --destructive: 0 70% 50%;
  --destructive-foreground: 320 50% 90%;
  --ring: 280 40% 35%;
  --muted: 260 15% 20%;
  --muted-foreground: 320 50% 70%;
  --popover: 260 15% 16%;
  --popover-foreground: 320 50% 90%;
}

/* Cyberpunk theme */
.theme-cyberpunk {
  --background: 230 40% 12%;
  --foreground: 60 70% 70%;
  --card: 230 40% 16%;
  --card-foreground: 60 70% 70%;
  --border: 180 60% 40%;
  --input: 230 40% 16%;
  --primary: 180 60% 50%;
  --primary-foreground: 230 40% 12%;
  --secondary: 320 60% 45%;
  --secondary-foreground: 60 70% 70%;
  --accent: 320 60% 45%;
  --accent-foreground: 60 70% 70%;
  --destructive: 0 70% 50%;
  --destructive-foreground: 60 70% 70%;
  --ring: 180 60% 40%;
  --muted: 230 40% 20%;
  --muted-foreground: 60 70% 50%;
  --popover: 230 40% 16%;
  --popover-foreground: 60 70% 70%;
}

/* Terminal theme */
.theme-terminal {
  --background: 120 5% 5%;
  --foreground: 120 60% 70%;
  --card: 120 5% 8%;
  --card-foreground: 120 60% 70%;
  --border: 120 40% 25%;
  --input: 120 5% 8%;
  --primary: 120 60% 50%;
  --primary-foreground: 120 5% 5%;
  --secondary: 120 40% 25%;
  --secondary-foreground: 120 60% 70%;
  --accent: 120 40% 25%;
  --accent-foreground: 120 60% 70%;
  --destructive: 0 70% 50%;
  --destructive-foreground: 120 60% 70%;
  --ring: 120 40% 25%;
  --muted: 120 5% 12%;
  --muted-foreground: 120 60% 50%;
  --popover: 120 5% 8%;
  --popover-foreground: 120 60% 70%;
}

/* Matrix theme */
.theme-matrix {
  --background: 120 25% 8%;
  --foreground: 120 60% 65%;
  --card: 120 25% 11%;
  --card-foreground: 120 60% 65%;
  --border: 120 50% 25%;
  --input: 120 25% 11%;
  --primary: 120 60% 50%;
  --primary-foreground: 120 25% 8%;
  --secondary: 120 50% 25%;
  --secondary-foreground: 120 60% 65%;
  --accent: 120 50% 25%;
  --accent-foreground: 120 60% 65%;
  --destructive: 0 70% 50%;
  --destructive-foreground: 120 60% 65%;
  --ring: 120 50% 25%;
  --muted: 120 25% 14%;
  --muted-foreground: 120 60% 45%;
  --popover: 120 25% 11%;
  --popover-foreground: 120 60% 65%;
}

/* Midnight theme */
.theme-midnight {
  --background: 220 35% 8%;
  --foreground: 220 35% 85%;
  --card: 220 35% 11%;
  --card-foreground: 220 35% 85%;
  --border: 220 35% 18%;
  --input: 220 35% 11%;
  --primary: 220 60% 55%;
  --primary-foreground: 220 35% 8%;
  --secondary: 220 35% 18%;
  --secondary-foreground: 220 35% 85%;
  --accent: 220 35% 25%;
  --accent-foreground: 220 35% 85%;
  --destructive: 0 70% 50%;
  --destructive-foreground: 220 35% 85%;
  --ring: 220 35% 25%;
  --muted: 220 35% 14%;
  --muted-foreground: 220 35% 65%;
  --popover: 220 35% 11%;
  --popover-foreground: 220 35% 85%;
}

/* Oceanic theme */
.theme-oceanic {
  --background: 200 30% 10%;
  --foreground: 200 20% 85%;
  --card: 200 30% 13%;
  --card-foreground: 200 20% 85%;
  --border: 200 25% 18%;
  --input: 200 30% 13%;
  --primary: 200 60% 50%;
  --primary-foreground: 200 30% 10%;
  --secondary: 200 25% 20%;
  --secondary-foreground: 200 20% 85%;
  --accent: 180 40% 40%;
  --accent-foreground: 200 20% 85%;
  --destructive: 0 70% 50%;
  --destructive-foreground: 200 20% 85%;
  --ring: 200 25% 25%;
  --muted: 200 30% 16%;
  --muted-foreground: 200 20% 65%;
  --popover: 200 30% 13%;
  --popover-foreground: 200 20% 85%;
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
  transition: background-color var(--transition-duration) ease-in-out, color var(--transition-duration) ease-in-out;
}

/* Theme transition effects */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-duration);
}

/* Print styles for resume */
@media print {
  @page {
    margin: 0.5in;
    size: A4;
  }

  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.4;
  }

  /* Hide non-essential elements when printing */
  nav, header, footer, .no-print {
    display: none !important;
  }

  /* Resume specific print styles */
  .print\:shadow-none {
    box-shadow: none !important;
  }

  .print\:border-none {
    border: none !important;
  }

  .print\:border-gray-300 {
    border-color: #d1d5db !important;
  }

  /* Ensure proper page breaks */
  .print\:break-inside-avoid {
    break-inside: avoid;
  }

  /* Optimize text for print */
  h1, h2, h3 {
    color: black !important;
    break-after: avoid;
  }

  /* Ensure links are visible in print */
  a {
    color: black !important;
    text-decoration: underline;
  }

  /* Skills tags for print */
  .print\:bg-gray-100 {
    background-color: #f3f4f6 !important;
    color: black !important;
  }
}



@layer base {
  * {
    @apply border-[hsl(var(--border))];
  }

  /* Command menu styling */
  [cmdk-group-heading] {
    @apply px-2 py-1.5 text-xs font-medium text-[hsl(var(--muted-foreground))] uppercase tracking-wider;
  }

  /* Enhanced command menu animations */
  [cmdk-item] {
    @apply transition-all duration-200 ease-out;
  }

  [cmdk-item]:hover {
    @apply transform scale-[1.02];
  }

  [cmdk-item][aria-selected="true"] {
    @apply transform scale-[1.02];
  }

  /* Custom scrollbar styling */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-[hsl(var(--muted))] rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-[hsl(var(--muted-foreground))];
  }

  /* Hide scrollbar but keep functionality */
  .hide-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
    width: 0;
    height: 0;
  }

  /* Remove focus outlines from 404 page game */
  .not-found-page *:focus,
  .not-found-page *:focus-visible {
    outline: none !important;
    box-shadow: none !important;
    border-color: transparent !important;
  }
}

/* Page Loader Animations */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slideInFromRight 0.7s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.7s ease-out;
}

/* Responsive utilities */
@media (max-width: 1024px) {
  .sidebar-mobile {
    width: 100vw;
    max-width: 300px;
  }
}

/* Prevent scroll when mobile menu is open */
body.menu-open {
  overflow: hidden;
  height: 100vh;
}

/* Mobile menu overlay */
.mobile-overlay {
  backdrop-filter: blur(4px);
}

/* Ensure proper height behavior across different zoom levels */
@media screen {
  .viewport-height {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  .min-viewport-height {
    min-height: 100vh;
    min-height: 100dvh;
  }
}

/* Fix for Safari and other browsers with dynamic viewport */
@supports (height: 100dvh) {
  .viewport-height {
    height: 100dvh;
  }

  .min-viewport-height {
    min-height: 100dvh;
  }
}

/* Additional responsive fixes for zoom levels */
@media screen and (min-width: 1px) {
  .responsive-layout {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .sidebar-container {
    position: relative;
    flex-shrink: 0;
  }

  .main-content-container {
    position: relative;
    flex: 1;
    min-width: 0; /* Prevents flex item from overflowing */
  }
}

/* Ensure proper scrolling behavior */
.scrollable-area {
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Fix for potential layout shifts */
.layout-stable {
  contain: layout style;
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Shimmer animation for loading states */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}
