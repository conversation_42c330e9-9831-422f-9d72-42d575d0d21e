import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Blog - Than<PERSON><PERSON>",
  description: "Articles and blog posts by <PERSON><PERSON><PERSON> on web development, AI, technology, and more. Auto-synced with Medium.",
  keywords: ["blog", "articles", "web development", "AI", "technology", "programming", "medium"],
  openGraph: {
    title: "Blog - Than<PERSON><PERSON>",
    description: "Articles and blog posts by <PERSON><PERSON><PERSON> on web development, AI, technology, and more.",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Blog - <PERSON><PERSON><PERSON> Sai",
    description: "Articles and blog posts by <PERSON><PERSON><PERSON> on web development, AI, technology, and more.",
  },
};

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
