/**
 * Blog Page
 *
 * Displays a paginated list of blog posts with search, filtering, and refresh capabilities.
 * Optimized for performance with lazy loading and efficient rendering.
 */

"use client"

import { RefreshCw, Rss, AlertCircle } from "lucide-react";
import { NewBadge, AnimatedBlogCard, Pagination } from "@/components/blog";
import { PaginationInfo } from "@/components/blog/Pagination";
import { useBlogData } from "@/hooks/useBlogData";
import { usePagination } from "@/hooks/usePagination";
import { memo, useMemo } from "react";

// Memoized blog card component for better performance
const MemoizedBlogCard = memo(AnimatedBlogCard);

export default function BlogPage() {
  const {
    posts,
    loading,
    error,
    lastUpdated,
    newPostsCount,
    refreshPosts
  } = useBlogData()

  // Pagination configuration
  const ITEMS_PER_PAGE = 6

  const {
    currentPage,
    totalPages,
    currentData: paginatedPosts,
    goToPage
  } = usePagination({
    data: posts,
    itemsPerPage: ITEMS_PER_PAGE,
    resetOnDataChange: true
  });

  // Memoize expensive calculations
  const paginationInfo = useMemo(() => ({
    currentPage,
    totalPages,
    totalItems: posts.length,
    itemsPerPage: ITEMS_PER_PAGE,
  }), [currentPage, totalPages, posts.length]);

  // Memoize loading skeleton array
  const loadingSkeletons = useMemo(() =>
    [...Array(ITEMS_PER_PAGE)].map((_, i) => i),
    [ITEMS_PER_PAGE]
  );

  return (
    <div className="space-y-8">
      <div className="space-y-8">
        <header className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <h1 className="text-3xl font-bold tracking-tight sm:text-4xl">Blog</h1>
                {newPostsCount > 0 && (
                  <NewBadge daysOld={0} variant="compact" />
                )}
              </div>
              <p className="text-[hsl(var(--muted-foreground))]">
                Articles and insights on web development, AI, and technology.
                {!loading && posts.length > 0 && (
                  <span className="ml-1 text-sm">
                    ({posts.length} article{posts.length > 1 ? 's' : ''} total)
                  </span>
                )}
                {newPostsCount > 0 && (
                  <span className="ml-1 font-medium text-[hsl(var(--primary))]">
                    {newPostsCount} new article{newPostsCount > 1 ? 's' : ''}!
                  </span>
                )}
              </p>
            </div>

            <button
              onClick={refreshPosts}
              disabled={loading}
              className="flex items-center gap-2 px-3 py-2 text-sm border border-[hsl(var(--border))] rounded-lg hover:bg-[hsl(var(--accent))] transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>

          {/* Status indicators */}
          <div className="flex items-center gap-4 text-sm text-[hsl(var(--muted-foreground))]">
            <div className="flex items-center gap-1">
              <Rss className="h-3 w-3" />
              <span>Auto-synced with Medium</span>
            </div>
            {lastUpdated && (
              <span>
                Last updated: {new Date(lastUpdated).toLocaleTimeString()}
              </span>
            )}
          </div>
        </header>

      {/* Error state */}
      {error && (
        <div className="flex items-center gap-2 p-4 border border-red-200 bg-red-50 text-red-700 rounded-lg">
          <AlertCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      )}

        {/* Loading state with shimmer effect */}
        {loading && posts.length === 0 && (
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {loadingSkeletons.map((i) => (
              <div
                key={i}
                className="animate-pulse"
                style={{ animationDelay: `${i * 150}ms` }}
              >
                <div className="rounded-xl border border-[hsl(var(--border))] bg-[hsl(var(--card))] overflow-hidden shadow-lg">
                  <div className="aspect-video bg-gradient-to-br from-[hsl(var(--muted))] to-[hsl(var(--muted))]/50 relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
                  </div>
                  <div className="p-6 space-y-4">
                    <div className="space-y-2">
                      <div className="h-4 bg-[hsl(var(--muted))] rounded w-3/4 relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                      </div>
                      <div className="h-6 bg-[hsl(var(--muted))] rounded relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-3 bg-[hsl(var(--muted))] rounded relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                      </div>
                      <div className="h-3 bg-[hsl(var(--muted))] rounded w-5/6 relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination Info */}
        {!loading && posts.length > 0 && (
          <PaginationInfo
            {...paginationInfo}
            className="mb-6"
          />
        )}

        {/* Animated Posts Grid */}
        {!loading && posts.length > 0 && (
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {paginatedPosts.map((post, index) => (
              <MemoizedBlogCard
                key={`${currentPage}-${post.slug || index}`}
                post={post}
                index={index}
                pageKey={currentPage}
              />
            ))}
          </div>
        )}

        {/* Pagination Controls */}
        {!loading && posts.length > 0 && totalPages > 1 && (
          <div className="mt-12 space-y-6">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
              className="justify-center"
            />

            {/* Additional pagination info at bottom */}
            <PaginationInfo
              {...paginationInfo}
              className="justify-center"
            />
          </div>
        )}

      {/* Empty state */}
      {!loading && posts.length === 0 && !error && (
        <div className="text-center py-12">
          <Rss className="h-12 w-12 text-[hsl(var(--muted-foreground))] mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No articles found</h3>
          <p className="text-[hsl(var(--muted-foreground))] mb-4">
            We couldn't find any articles. This might be because:
          </p>
          <ul className="text-sm text-[hsl(var(--muted-foreground))] space-y-1 max-w-md mx-auto">
            <li>• Medium RSS feed is not accessible</li>
            <li>• No articles have been published yet</li>
            <li>• There's a temporary network issue</li>
          </ul>
          <button
            onClick={refreshPosts}
            className="mt-4 px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-lg hover:bg-[hsl(var(--primary))]/90 transition-colors"
          >
            Try Again
          </button>
        </div>
      )}

      </div>

    </div>
  );
}
