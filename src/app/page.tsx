'use client';

import FlippablePhoto from '@/components/common/FlippablePhoto';
import { NewBlogPostNotification } from '@/components/home';
import { getCachedBlogData, preloadBlogData } from '@/lib/preload';
import { Brain, Clock, Code2, Database, Mail, MapPin, Zap } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

// Real-time status component
function LiveStatus() {
  const [currentTime, setCurrentTime] = useState<string>('');

  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      setCurrentTime(
        now.toLocaleTimeString('en-US', {
          timeZone: 'Asia/Kolkata',
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
        })
      );
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  return (
    <div className="flex items-center gap-2 text-sm text-neutral-400">
      <Clock className="h-3 w-3" />
      <span>{currentTime} IST</span>
    </div>
  );
}

export default function Home() {
  const [isDataReady, setIsDataReady] = useState(false);

  // Check if data is already available on mount
  useEffect(() => {
    const cachedData = getCachedBlogData();
    if (cachedData && cachedData.length > 0) {
      setIsDataReady(true);
    }
  }, []);

  // Preload critical data for the home page
  useEffect(() => {
    const loadCriticalData = async () => {
      try {
        // Skip if data is already ready
        if (isDataReady) return;

        // Check if we already have cached data
        const cachedData = getCachedBlogData();
        if (cachedData && cachedData.length > 0) {
          setIsDataReady(true);
          return;
        }

        // If no cached data, preload it
        await preloadBlogData();
        setIsDataReady(true);
      } catch (error) {
        console.error('Failed to preload data:', error);
        // Set ready anyway to prevent infinite loading
        setIsDataReady(true);
      }
    };

    if (!isDataReady) {
      loadCriticalData();
    }
  }, [isDataReady]);

  // Expose data readiness to parent components via global state
  useEffect(() => {
    if (isDataReady) {
      // Signal that home page data is ready
      window.dispatchEvent(new CustomEvent('homeDataReady'));
    }
  }, [isDataReady]);

  return (
    <div className="space-y-8 sm:space-y-12 lg:space-y-16">
      {/* Hero Section */}
      <section className="space-y-4 sm:space-y-6">
        <div className="space-y-2">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tight">
            Hola, I&apos;m Thanmai, A Full-Stack Developer.
          </h1>
          <p className="text-base sm:text-lg text-neutral-400 max-w-[700px]">
            Generative AI Engineer crafting intelligent solutions that bridge the gap between human
            creativity and machine capability. Currently building next-generation AI systems at{' '}
            <strong>Ascendion</strong>.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center gap-4 pt-2">
          <div className="flex items-center gap-2 text-neutral-400">
            <MapPin className="h-4 w-4" />
            <span>Based in India</span>
          </div>
          <LiveStatus />
        </div>

        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
          <Link
            href="/experience"
            className="inline-flex h-10 items-center justify-center rounded-md bg-[hsl(var(--primary))] px-6 text-sm font-medium text-[hsl(var(--primary-foreground))] transition-colors hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))] focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 focus:ring-offset-[hsl(var(--background))]"
          >
            View Experience
          </Link>
          <Link
            href="/contact"
            className="inline-flex h-10 items-center justify-center rounded-md border border-[hsl(var(--border))] bg-transparent px-6 text-sm font-medium text-[hsl(var(--foreground))] transition-colors hover:bg-[hsl(var(--secondary))] hover:text-[hsl(var(--secondary-foreground))] focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 focus:ring-offset-[hsl(var(--background))]"
          >
            <Mail className="mr-2 h-4 w-4" />
            Get in Touch
          </Link>
        </div>
      </section>

      {/* About Section - Bento Grid */}
      <section className="space-y-6">
        <h2 className="text-xl sm:text-2xl font-bold tracking-tight">About Me</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {/* Main Bio Card - Large */}
          <div className="md:col-span-2 lg:col-span-2 rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] p-6 space-y-4">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Brain className="h-6 w-6 text-[hsl(var(--primary))]" />
                <h3 className="text-xl font-bold">Who I Am</h3>
              </div>

              <div className="space-y-4">
                <p className="text-[hsl(var(--muted-foreground))] leading-relaxed">
                  I'm a <strong>Generative AI Engineer</strong> passionate about creating
                  intelligent solutions that bridge human creativity and machine capability. My
                  journey spans from data pipeline optimization to architecting AI agents that
                  understand context and deliver meaningful automation.
                </p>

                <p className="text-[hsl(var(--muted-foreground))] leading-relaxed">
                  I thrive on solving complex problems and turning innovative ideas into reality,
                  building technology that amplifies human potential rather than replacing it.
                </p>

                <div className="flex flex-wrap items-center gap-4 pt-2">
                  <Link
                    href="/experience"
                    className="text-[hsl(var(--primary))] hover:underline text-sm font-medium"
                  >
                    View Experience →
                  </Link>
                  <Link
                    href="/resume"
                    className="text-[hsl(var(--primary))] hover:underline text-sm font-medium"
                  >
                    Download Resume →
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Profile Photo Card */}
          <div className="md:col-span-1 lg:col-span-1 rounded-lg overflow-hidden border border-[hsl(var(--border))]">
            <div className="aspect-square w-full h-full">
              <FlippablePhoto
                frontImageSrc="/images/thanmaisai_real_photo.png"
                backImageSrc="/images/hyperfox_pose.png"
                altText="Thanmai Sai"
              />
            </div>
          </div>

          {/* Current Focus Card */}
          <div className="md:col-span-1 lg:col-span-1 rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] p-4 space-y-3">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-500" />
              <h3 className="font-bold text-sm">Current Focus</h3>
            </div>
            <div className="space-y-3">
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-[hsl(var(--primary))] rounded-full mt-1.5 flex-shrink-0"></div>
                  <span className="text-xs text-[hsl(var(--muted-foreground))]">
                    AI agent architecture
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-[hsl(var(--primary))] rounded-full mt-1.5 flex-shrink-0"></div>
                  <span className="text-xs text-[hsl(var(--muted-foreground))]">
                    Context-aware automation
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-[hsl(var(--primary))] rounded-full mt-1.5 flex-shrink-0"></div>
                  <span className="text-xs text-[hsl(var(--muted-foreground))]">
                    Intelligent workflows
                  </span>
                </li>
              </ul>
            </div>
          </div>

          {/* Quick Navigation */}
          <div className="md:col-span-3 lg:col-span-4 grid grid-cols-2 lg:grid-cols-3 gap-4">
            <Link href="/stack" className="group">
              <div className="rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] p-4 transition-colors hover:border-[hsl(var(--accent))] space-y-2">
                <div className="flex items-center gap-2">
                  <Code2 className="h-4 w-4 text-[hsl(var(--primary))]" />
                  <h4 className="font-medium group-hover:underline">Tech Stack</h4>
                </div>
                <p className="text-xs text-[hsl(var(--muted-foreground))]">
                  Tools & technologies I use
                </p>
              </div>
            </Link>

            <Link href="/projects" className="group">
              <div className="rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] p-4 transition-colors hover:border-[hsl(var(--accent))] space-y-2">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-[hsl(var(--primary))]" />
                  <h4 className="font-medium group-hover:underline">Projects</h4>
                </div>
                <p className="text-xs text-[hsl(var(--muted-foreground))]">
                  Featured work & portfolio
                </p>
              </div>
            </Link>

            <Link href="/contact" className="group lg:col-span-1 col-span-2">
              <div className="rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] p-4 transition-colors hover:border-[hsl(var(--accent))] space-y-2">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-[hsl(var(--primary))]" />
                  <h4 className="font-medium group-hover:underline">Get in Touch</h4>
                </div>
                <p className="text-xs text-[hsl(var(--muted-foreground))]">
                  Let's connect and collaborate
                </p>
              </div>
            </Link>
          </div>

          {/* Philosophy Card */}
          <div className="md:col-span-3 lg:col-span-4 rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] p-6">
            <div className="max-w-3xl">
              <h3 className="text-xl font-bold mb-4">Engineering Philosophy</h3>
              <p className="text-[hsl(var(--muted-foreground))] leading-relaxed">
                I believe in building technology that amplifies human potential rather than
                replacing it. Every line of code should serve a purpose, every algorithm should
                solve a real problem, and every system should be designed with the end user in mind.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* New Drops Section */}
      <section className="space-y-4 sm:space-y-6">
        <h2 className="text-xl sm:text-2xl font-bold tracking-tight">New Drops</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
          {/* Project Card 1 */}
          <Link href="/projects/system-prompt-generator" className="group">
            <div className="overflow-hidden rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] transition-colors hover:border-[hsl(var(--accent))]">
              <div className="aspect-video w-full relative">
                <Image
                  src="/images/hyperfox.png"
                  alt="System Prompt Generator"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-4">
                <h3 className="font-medium text-base sm:text-lg group-hover:underline">
                  System Prompt Generator
                </h3>
                <p className="mt-2 text-sm text-[hsl(var(--muted-foreground))]">
                  A modern web application for creating and managing AI system prompts.
                </p>
                <div className="mt-3 flex flex-wrap gap-2">
                  <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                    AI
                  </span>
                  <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                    Next.js
                  </span>
                </div>
              </div>
            </div>
          </Link>

          {/* Project Card 2 */}
          <Link href="/projects/portfolio-website" className="group">
            <div className="overflow-hidden rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] transition-colors hover:border-[hsl(var(--accent))]">
              <div className="aspect-video w-full relative">
                <Image
                  src="/images/hyperfox.png"
                  alt="Portfolio Website"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-4">
                <h3 className="font-medium text-base sm:text-lg group-hover:underline">
                  Portfolio Website
                </h3>
                <p className="mt-2 text-sm text-[hsl(var(--muted-foreground))]">
                  A modern, responsive portfolio website built with Next.js and Tailwind CSS.
                </p>
                <div className="mt-3 flex flex-wrap gap-2">
                  <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                    Next.js
                  </span>
                  <span className="inline-flex items-center rounded-full bg-[hsl(var(--secondary))] px-2.5 py-0.5 text-xs font-medium text-[hsl(var(--secondary-foreground))]">
                    Tailwind
                  </span>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </section>

      {/* Dynamic New Blog Post Notifications */}
      <NewBlogPostNotification />
    </div>
  );
}
