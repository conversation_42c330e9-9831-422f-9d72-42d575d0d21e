import { Metadata } from "next";
import { Calendar, MapPin } from "lucide-react";

export const metadata: Metadata = {
  title: "Experience - Thanmai Sai",
  description: "Professional experience and work history of Than<PERSON><PERSON> Sai - Generative AI Engineer with expertise in full-stack development",
};

export default function ExperiencePage() {
  return (
    <div className="space-y-8">
      <header className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight sm:text-4xl">Experience</h1>
        <p className="text-[hsl(var(--muted-foreground))]">
          My professional journey in building AI solutions and full-stack applications.
        </p>
      </header>

      <div className="space-y-8">
        {/* Experience Item 1 - Current Role */}
        <div className="relative">
          <div className="absolute left-0 top-0 bottom-0 w-0.5 bg-gradient-to-b from-[hsl(var(--primary))] to-[hsl(var(--primary))]/50 rounded-full"></div>
          <div className="pl-8 space-y-4">
            <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-4">
              <div className="space-y-2">
                <h2 className="text-2xl font-bold text-[hsl(var(--foreground))]">Generative AI Engineer</h2>
                <div className="flex items-center gap-2 text-lg font-semibold text-[hsl(var(--primary))]">
                  <span>Ascendion</span>
                  <span className="text-[hsl(var(--muted-foreground))]">•</span>
                  <div className="flex items-center gap-1 text-[hsl(var(--muted-foreground))]">
                    <MapPin className="h-4 w-4" />
                    <span>Hyderabad, India</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2 px-3 py-1 bg-[hsl(var(--primary))]/10 text-[hsl(var(--primary))] rounded-lg text-sm font-medium">
                <Calendar className="h-4 w-4" />
                <span>June 2024 - Present</span>
              </div>
            </div>



            <ul className="space-y-3 text-[hsl(var(--muted-foreground))]">
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[hsl(var(--primary))] rounded-full mt-2 flex-shrink-0"></div>
                <span>Engineered and deployed full-stack AI pipelines turning user prompts into MVC-based web apps (Angular/React), cutting delivery time by 35%, boosting platform engagement by 45%, and fueling a 50% increase in upsell opportunities</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[hsl(var(--primary))] rounded-full mt-2 flex-shrink-0"></div>
                <span>Built an AI pipeline using large language models and custom agent frameworks to convert natural-language prompts into wireframes—resolving token-limit and memory-retention issues—and improving accuracy from 10% to 40% for rapid prototyping</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[hsl(var(--primary))] rounded-full mt-2 flex-shrink-0"></div>
                <span>Reduced backend latency by 50% by redesigning API workflows with parallel processing, enabling real-time scaling under high-demand AI workloads and enhancing user experience</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[hsl(var(--primary))] rounded-full mt-2 flex-shrink-0"></div>
                <span>Drove a 60% sales increase by integrating Neo4j for competitor analysis, developing graph queries that transformed raw data into insights, enabling clients to strategically outpace rivals</span>
              </li>
            </ul>

            <div className="flex flex-wrap gap-2 mt-4">
              {["Python","Crew AI","Langchain","PostgreSQL","ChromaDB", "Neo4j", "API Design"].map((tech) => (
                <span key={tech} className="px-3 py-1 bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))] rounded-lg text-sm font-medium">
                  {tech}
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* Experience Item 2 - Technical Intern */}
        <div className="relative">
          <div className="absolute left-0 top-0 bottom-0 w-0.5 bg-gradient-to-b from-[hsl(var(--muted-foreground))]/50 to-[hsl(var(--muted-foreground))]/20 rounded-full"></div>
          <div className="pl-8 space-y-4">
            <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-4">
              <div className="space-y-2">
                <h2 className="text-2xl font-bold text-[hsl(var(--foreground))]">Technical Intern</h2>
                <div className="flex items-center gap-2 text-lg font-semibold text-[hsl(var(--primary))]">
                  <span>Hevo Data</span>
                  <span className="text-[hsl(var(--muted-foreground))]">•</span>
                  <div className="flex items-center gap-1 text-[hsl(var(--muted-foreground))]">
                    <MapPin className="h-4 w-4" />
                    <span>Bangalore, India</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2 px-3 py-1 bg-[hsl(var(--muted))] text-[hsl(var(--muted-foreground))] rounded-lg text-sm font-medium">
                <Calendar className="h-4 w-4" />
                <span>July 2023 - October 2023</span>
              </div>
            </div>

            <ul className="space-y-3 text-[hsl(var(--muted-foreground))]">
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[hsl(var(--muted-foreground))] rounded-full mt-2 flex-shrink-0"></div>
                <span>Worked on integrating and managing various data sources within Hevo's data pipeline platform, ensuring seamless data flow and reliability</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[hsl(var(--muted-foreground))] rounded-full mt-2 flex-shrink-0"></div>
                <span>Contributed to Python-based transformation scripts to improve data quality and consistency across multiple data sources</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-[hsl(var(--muted-foreground))] rounded-full mt-2 flex-shrink-0"></div>
                <span>Utilized Grafana for monitoring pipeline performance and troubleshooting issues, enhancing system observability and reliability</span>
              </li>
            </ul>

            <div className="flex flex-wrap gap-2 mt-4">
              {["Python", "Data Pipelines", "Grafana", "ETL", "Data Integration", "Monitoring"].map((tech) => (
                <span key={tech} className="px-3 py-1 bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))] rounded-lg text-sm font-medium">
                  {tech}
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* Career Highlights - Commented out for minimal design */}
        {/*
        <div className="mt-12 p-6 bg-gradient-to-r from-[hsl(var(--primary))]/5 to-[hsl(var(--primary))]/10 border border-[hsl(var(--primary))]/20 rounded-xl">
          <div className="flex items-center gap-3 mb-4">
            <Award className="h-6 w-6 text-[hsl(var(--primary))]" />
            <h3 className="text-xl font-bold text-[hsl(var(--foreground))]">Career Highlights</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-[hsl(var(--primary))]">60%</div>
              <div className="text-sm text-[hsl(var(--muted-foreground))]">Sales Increase</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-[hsl(var(--primary))]">50%</div>
              <div className="text-sm text-[hsl(var(--muted-foreground))]">Latency Reduction</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-[hsl(var(--primary))]">45%</div>
              <div className="text-sm text-[hsl(var(--muted-foreground))]">Platform Engagement</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-[hsl(var(--primary))]">4x</div>
              <div className="text-sm text-[hsl(var(--muted-foreground))]">Accuracy Improvement</div>
            </div>
          </div>
        </div>
        */}
      </div>
    </div>
  );
}
