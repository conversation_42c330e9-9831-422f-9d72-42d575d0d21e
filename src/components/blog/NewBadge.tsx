/**
 * NewBadge Component
 *
 * A dynamic badge component that displays "NEW" indicators for recent blog posts.
 * Automatically calculates and displays time-based labels with appropriate styling.
 *
 * Features:
 * - Time-aware badge text (Today, Yesterday, X days ago, etc.)
 * - Color-coded based on recency (green for very new, blue for new, orange for older)
 * - Two variants: default (with time text) and compact (just "NEW")
 * - Automatic hiding for posts older than 14 days
 *
 * @example
 * ```tsx
 * <NewBadge daysOld={2} pubDate="2024-01-15" />
 * <NewBadge daysOld={1} variant="compact" />
 * ```
 */

import { cn } from "@/lib/utils";
import { getTimeAgo } from "@/lib/api/medium";

interface NewBadgeProps {
  daysOld: number;
  pubDate?: string;
  className?: string;
  variant?: 'default' | 'compact';
}

/**
 * Constants for badge behavior
 */
const BADGE_DISPLAY_THRESHOLD = 14; // Days after which badge is hidden
const VERY_NEW_THRESHOLD = 3; // Days for "very new" styling
const NEW_THRESHOLD = 7; // Days for "new" styling

export default function NewBadge({ daysOld, pubDate, className, variant = 'default' }: NewBadgeProps) {
  // Only show badge for articles within threshold
  if (daysOld > BADGE_DISPLAY_THRESHOLD) return null;

  const isVeryNew = daysOld <= VERY_NEW_THRESHOLD;
  const isNew = daysOld <= NEW_THRESHOLD;

  const getBadgeText = (): string => {
    if (pubDate) {
      return getTimeAgo(pubDate);
    }

    if (daysOld === 0) return 'Today';
    if (daysOld === 1) return 'Yesterday';
    if (daysOld <= 3) return `${daysOld}d ago`;
    if (daysOld <= 7) return 'This week';
    return `${daysOld}d ago`;
  };

  const getBadgeColor = (): string => {
    if (isVeryNew) return 'bg-green-500 text-white';
    if (isNew) return 'bg-blue-500 text-white';
    return 'bg-orange-500 text-white';
  };

  if (variant === 'compact') {
    return (
      <span className={cn(
        "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
        getBadgeColor(),
        className
      )}>
        NEW
      </span>
    )
  }

  return (
    <span className={cn(
      "inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium",
      getBadgeColor(),
      className
    )}>
      {getBadgeText()}
    </span>
  )
}
