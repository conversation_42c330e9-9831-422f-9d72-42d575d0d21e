'use client';

import { cn } from '@/lib/utils';
import { EnhancedBlogPost } from '@/types/content';
import { Calendar, Clock, ExternalLink, FileText, Tag } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useRef, useState } from 'react';
import NewBadge from './NewBadge';

interface AnimatedBlogCardProps {
  post: EnhancedBlogPost;
  index: number;
  className?: string;
  pageKey?: string | number; // Add page key for re-triggering animations
}

export default function AnimatedBlogCard({
  post,
  index,
  className,
  pageKey,
}: AnimatedBlogCardProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const isExternal = post.source === 'medium' || post.source === 'external';
  const CardWrapper = isExternal ? 'a' : Link;

  const cardProps = isExternal
    ? {
        href: post.externalUrl!,
        target: '_blank',
        rel: 'noopener noreferrer',
        className: 'group block',
      }
    : {
        href: `/blog/${post.slug}`,
        className: 'group block',
      };

  // Intersection Observer for reveal animation
  useEffect(() => {
    // Reset visibility when page changes
    setIsVisible(false);

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry && entry.isIntersecting) {
          setTimeout(() => setIsVisible(true), index * 150); // Staggered animation
        }
      },
      { threshold: 0.1 }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => observer.disconnect();
  }, [index, pageKey]); // Re-run when page changes

  // Mouse tracking for 3D effect
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!cardRef.current) return;

    const rect = cardRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    setMousePosition({ x, y });
  };

  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => {
    setIsHovered(false);
    setMousePosition({ x: 0, y: 0 });
  };

  // Calculate 3D transform
  const getTransform = () => {
    if (!isHovered || !cardRef.current)
      return 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';

    const rect = cardRef.current.getBoundingClientRect();
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;

    const rotateX = (mousePosition.y - centerY) / 10;
    const rotateY = (centerX - mousePosition.x) / 10;

    return `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(${isHovered ? '20px' : '0px'})`;
  };

  return (
    <div
      ref={cardRef}
      className={cn(
        'transform transition-all duration-700 ease-out',
        isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0',
        className
      )}
      style={{
        transform: isVisible ? getTransform() : 'translateY(32px)',
        transitionDelay: isVisible ? '0ms' : `${index * 150}ms`,
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <CardWrapper {...cardProps}>
        <article
          className={cn(
            'relative rounded-xl border border-[hsl(var(--border))] bg-[hsl(var(--card))] overflow-hidden transition-all duration-300',
            'hover:border-[hsl(var(--primary))] hover:shadow-2xl hover:shadow-[hsl(var(--primary))]/20',
            'before:absolute before:inset-0 before:bg-gradient-to-br before:from-transparent before:via-transparent before:to-[hsl(var(--primary))]/5 before:opacity-0 before:transition-opacity before:duration-300',
            'hover:before:opacity-100',
            isHovered && 'scale-105'
          )}
        >
          {/* Animated gradient overlay */}
          <div
            className={cn(
              'absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 opacity-0 transition-opacity duration-300',
              isHovered && 'opacity-100'
            )}
          />

          {/* Thumbnail with parallax effect */}
          {post.thumbnail && (
            <div className="relative aspect-video w-full overflow-hidden">
              <div
                className="absolute inset-0 transition-transform duration-300"
                style={{
                  transform: isHovered
                    ? `scale(1.1) translate(${mousePosition.x / 50}px, ${mousePosition.y / 50}px)`
                    : 'scale(1)',
                }}
              >
                <Image
                  src={post.thumbnail}
                  alt={post.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>

              {/* Gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />

              {/* Source badge with glow */}
              {post.source === 'medium' && (
                <div
                  className={cn(
                    'absolute top-3 right-3 bg-black/80 text-white px-3 py-1 rounded-full text-xs font-medium transition-all duration-300',
                    isHovered &&
                      'bg-[hsl(var(--primary))] shadow-lg shadow-[hsl(var(--primary))]/50'
                  )}
                >
                  Medium
                </div>
              )}

              {/* New badge */}
              {post.isNew && (
                <div className="absolute top-3 left-3 z-10">
                  <NewBadge daysOld={post.daysOld || 0} pubDate={post.date} />
                </div>
              )}
            </div>
          )}

          <div className="relative p-6 space-y-4 z-10">
            {/* Header with metadata */}
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm text-[hsl(var(--muted-foreground))]">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <time dateTime={post.date}>
                      {new Date(post.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                      })}
                    </time>
                  </div>
                  {post.readTime && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{post.readTime}</span>
                    </div>
                  )}
                </div>
                {isExternal && (
                  <ExternalLink
                    className={cn(
                      'h-3 w-3 transition-all duration-300',
                      isHovered && 'text-[hsl(var(--primary))] scale-110'
                    )}
                  />
                )}
              </div>

              {/* Title with gradient effect */}
              <h3
                className={cn(
                  'font-semibold text-lg leading-tight transition-all duration-300 line-clamp-2',
                  'group-hover:bg-gradient-to-r group-hover:from-[hsl(var(--primary))] group-hover:to-purple-600 group-hover:bg-clip-text group-hover:text-transparent'
                )}
              >
                {post.title}
              </h3>
            </div>

            {/* Excerpt */}
            <p className="text-[hsl(var(--muted-foreground))] text-sm leading-relaxed line-clamp-3">
              {post.excerpt}
            </p>

            {/* Tags with hover effects */}
            {post.tags && post.tags.length > 0 && (
              <div className="flex items-center gap-2 flex-wrap">
                <Tag className="h-3 w-3 text-[hsl(var(--muted-foreground))]" />
                <div className="flex gap-1 flex-wrap">
                  {post.tags.slice(0, 3).map((tag, tagIndex) => (
                    <span
                      key={tagIndex}
                      className={cn(
                        'inline-block bg-[hsl(var(--muted))] text-[hsl(var(--muted-foreground))] px-2 py-1 rounded-full text-xs transition-all duration-300',
                        isHovered &&
                          'bg-[hsl(var(--primary))]/10 text-[hsl(var(--primary))] scale-105'
                      )}
                      style={{
                        transitionDelay: `${tagIndex * 50}ms`,
                      }}
                    >
                      {tag}
                    </span>
                  ))}
                  {post.tags.length > 3 && (
                    <span className="text-xs text-[hsl(var(--muted-foreground))]">
                      +{post.tags.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Footer */}
            <div className="flex items-center justify-between pt-2 border-t border-[hsl(var(--border))]">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-[hsl(var(--muted-foreground))]" />
                <span className="text-sm text-[hsl(var(--muted-foreground))]">
                  {post.author || 'HyperFox'}
                </span>
              </div>

              {isExternal && (
                <span
                  className={cn(
                    'text-xs font-medium transition-all duration-300',
                    isHovered
                      ? 'text-[hsl(var(--primary))] scale-105'
                      : 'text-[hsl(var(--primary))]'
                  )}
                >
                  Read on {post.source === 'medium' ? 'Medium' : 'External'} →
                </span>
              )}
            </div>
          </div>

          {/* Animated border glow */}
          <div
            className={cn(
              'absolute inset-0 rounded-xl border-2 border-transparent bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 opacity-0 transition-opacity duration-300',
              'before:absolute before:inset-[2px] before:rounded-[10px] before:bg-[hsl(var(--card))]',
              isHovered && 'opacity-20'
            )}
          />
        </article>
      </CardWrapper>
    </div>
  );
}
