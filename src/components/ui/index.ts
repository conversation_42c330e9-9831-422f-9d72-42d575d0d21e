/**
 * UI Components Index
 *
 * Centralized exports for all UI components following shadcn/ui patterns.
 * This provides a clean API for importing UI components throughout the application.
 */

// Base UI Components
export { Button } from './button';
export { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './card';

// Component Types
export type { ButtonProps } from './button';
