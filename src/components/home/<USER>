"use client"

import Link from 'next/link'
import { ExternalLink, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useNewBlogPosts, formatRelativeTime } from '@/hooks/useNewBlogPosts'

interface NewBlogPostNotificationProps {
  className?: string
}

export default function NewBlogPostNotification({ className }: NewBlogPostNotificationProps) {
  const { newBlogPosts, loading, dismissBlogPost, hasNewBlogPosts } = useNewBlogPosts()

  // Don't render if no new blog posts (even while loading)
  if (!hasNewBlogPosts && !loading) {
    return null
  }

  // Show minimal loading only if we have no blog posts yet
  if (loading && newBlogPosts.length === 0) {
    return (
      <section className={cn("space-y-3", className)}>
        <div className="flex items-center gap-2">
          <div className="h-4 w-4 bg-[hsl(var(--muted))] rounded-full animate-pulse" />
          <div className="h-5 w-20 bg-[hsl(var(--muted))] rounded animate-pulse" />
        </div>
      </section>
    )
  }

  // Don't render if no blog posts after loading
  if (!hasNewBlogPosts) {
    return null
  }

  return (
    <section className={cn("space-y-4", className)}>
      <div className="flex items-center gap-2">
        <h2 className="text-xl font-medium tracking-tight">
          Latest Blog Post{newBlogPosts.length > 1 ? 's' : ''}
        </h2>
      </div>
      
      <div className="space-y-3">
        {newBlogPosts.map((blogPost) => (
          <div
            key={blogPost.slug}
            className="group relative rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] p-4 transition-colors hover:bg-[hsl(var(--accent))]/50"
          >
            {/* Dismiss button */}
            <button
              onClick={() => dismissBlogPost(blogPost.slug)}
              className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-full hover:bg-[hsl(var(--accent))] text-[hsl(var(--muted-foreground))]"
              aria-label="Dismiss blog post"
            >
              <X className="h-4 w-4" />
            </button>

            <Link
              href={blogPost.externalUrl || `/blog/${blogPost.slug}`}
              target={blogPost.source === 'medium' ? '_blank' : '_self'}
              rel={blogPost.source === 'medium' ? 'noopener noreferrer' : undefined}
              className="block space-y-2"
            >
              <div className="flex items-start justify-between gap-3">
                <div className="flex-1">
                  <h3 className="font-medium text-base group-hover:underline line-clamp-2">
                    {blogPost.title}
                  </h3>
                  {blogPost.excerpt && (
                    <p className="text-sm text-[hsl(var(--muted-foreground))] mt-1 line-clamp-2">
                      {blogPost.excerpt}
                    </p>
                  )}
                </div>
                {blogPost.source === 'medium' && (
                  <ExternalLink className="h-4 w-4 text-[hsl(var(--muted-foreground))] flex-shrink-0 mt-1" />
                )}
              </div>

              <div className="flex items-center gap-3 text-xs text-[hsl(var(--muted-foreground))]">
                <span>{formatRelativeTime(blogPost.date)}</span>
                {blogPost.readTime && (
                  <>
                    <span>•</span>
                    <span>{blogPost.readTime}</span>
                  </>
                )}
                {blogPost.source === 'medium' && (
                  <>
                    <span>•</span>
                    <span>Medium</span>
                  </>
                )}
              </div>
            </Link>
          </div>
        ))}
      </div>

      <div className="text-center pt-2">
        <Link
          href="/blog"
          className="text-sm text-[hsl(var(--muted-foreground))] hover:text-[hsl(var(--foreground))] hover:underline transition-colors"
        >
          View all blog posts →
        </Link>
      </div>
    </section>
  )
}
