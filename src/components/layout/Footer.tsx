"use client";

import { Heart } from "lucide-react";

export default function Footer() {

  return (
    <footer className="border-t border-[hsl(var(--border))] bg-[hsl(var(--card))] shadow-inner p-4 text-sm text-[hsl(var(--muted-foreground))] flex-shrink-0 mt-auto">
      <div className="container mx-auto flex flex-col md:flex-row justify-between items-center">
        <div className="mb-2 md:mb-0">
          © {new Date().getFullYear()} Portfolio. All rights reserved.
        </div>
        <div className="flex items-center">
          Made with <Heart className="h-4 w-4 mx-1 text-red-500" /> by <PERSON><PERSON><PERSON>
        </div>
      </div>
    </footer>
  );
}
