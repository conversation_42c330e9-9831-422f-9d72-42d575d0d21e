'use client';

import { ThemeSwitcher } from '@/components/features';
import { useExtras } from '@/contexts/ExtrasContext';
import { ThemeName } from '@/contexts/ThemeContext';
import { cn } from '@/lib/utils';
import {
  BookOpen,
  Briefcase,
  ChevronLeft,
  ChevronRight,
  Download,
  ExternalLink,
  FileText,
  FolderKanban,
  Gift,
  Home,
  Layers,
  Mail,
  Palette,
  Rss,
  X,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';

interface SidebarProps {
  setIsMobileMenuOpen?: (open: boolean) => void;
}

// Define navigation items
interface NavItem {
  name: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  shortcut?: string;
  external?: boolean;
}
const mainNavItems = [
  { name: 'Home', path: '/', icon: Home, shortcut: '1' },
  { name: 'Experience', path: '/experience', icon: Briefcase, shortcut: '2' },
  { name: 'Projects', path: '/projects', icon: Folder<PERSON>anban, shortcut: '3' },
  // { name: "Services", path: "/services", icon: Wrench, shortcut: "4" }, // Temporarily commented out
  { name: 'Resume', path: '/resume', icon: Download, shortcut: '4' },
];

const resourcesNavItems = [
  { name: 'Updates', path: '/feed', icon: Rss, shortcut: '5' },
  { name: 'Blog', path: '/blog', icon: FileText, shortcut: '6' },
  { name: 'Tech Stack', path: '/stack', icon: Layers, shortcut: '7' },
];

// Temporarily commented out - can be restored later
// const extrasNavItems = [
//   { name: "Guest Book", path: "/guestbook", icon: BookOpen, shortcut: "G" },
// ];

// Hidden extras section - discoverable but not prominently displayed
const getHiddenExtrasNavItems = (showHidden: boolean): NavItem[] => {
  if (!showHidden) return [];

  return [
    { name: 'Extras', path: '/extras', icon: BookOpen, shortcut: 'E' }, // Easter egg - unlocked by clicking logo 5 times
  ];
};

const connectNavItems = [
  { name: 'Contact', path: '/contact', icon: Mail, shortcut: 'C', external: false },
  {
    name: 'GitHub',
    path: 'https://github.com/thanmaisai',
    icon: ExternalLink,
    shortcut: '',
    external: true,
  },
  {
    name: 'LinkedIn',
    path: 'https://linkedin.com/in/thanmaisai',
    icon: ExternalLink,
    shortcut: '',
    external: true,
  },
  {
    name: 'Twitter',
    path: 'https://twitter.com/thanmaisai',
    icon: ExternalLink,
    shortcut: '',
    external: true,
  },
  {
    name: 'Instagram',
    path: 'https://instagram.com/thanmaisai',
    icon: ExternalLink,
    shortcut: '',
    external: true,
  },
];

export default function Sidebar({ setIsMobileMenuOpen }: SidebarProps) {
  const pathname = usePathname();
  const { isExtrasUnlocked, triggerUnlock } = useExtras();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [clickCount, setClickCount] = useState(0);

  // Reset click count after a period of inactivity
  useEffect(() => {
    if (clickCount > 0 && clickCount < 5) {
      const timer = setTimeout(() => {
        setClickCount(0);
      }, 3000); // Reset after 3 seconds of inactivity

      return () => clearTimeout(timer);
    }
  }, [clickCount]);

  const NavItem = ({ item }: { item: NavItem }) => {
    const isActive = pathname === item.path;

    return (
      <Link
        href={item.path}
        target={item.external ? '_blank' : undefined}
        rel={item.external ? 'noopener noreferrer' : undefined}
        aria-current={isActive ? 'page' : undefined}
        aria-label={`${item.name}${item.shortcut ? `, shortcut key ${item.shortcut}` : ''}`}
        role="menuitem"
        tabIndex={0}
        onClick={() => {
          // Close mobile menu when navigating to internal pages
          if (!item.external && setIsMobileMenuOpen) {
            setIsMobileMenuOpen(false);
          }
        }}
        className={cn(
          'flex items-center gap-2 px-3 py-2 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 focus:ring-offset-[hsl(var(--background))]',
          isActive
            ? 'bg-[hsl(var(--accent))] text-[hsl(var(--accent-foreground))]'
            : 'text-[hsl(var(--muted-foreground))] hover:text-[hsl(var(--foreground))] hover:bg-[hsl(var(--accent))]/50'
        )}
      >
        <item.icon className="h-4 w-4" />
        {!isCollapsed && (
          <>
            <span className="flex-1">{item.name}</span>
            {item.external && <ExternalLink className="h-3 w-3" />}
            {item.shortcut && !item.external && (
              <kbd className="inline-flex h-5 w-5 select-none items-center justify-center rounded border border-[hsl(var(--border))] bg-[hsl(var(--secondary))] px-1.5 font-mono text-[10px] font-medium text-[hsl(var(--muted-foreground))]">
                {item.shortcut}
              </kbd>
            )}
          </>
        )}
      </Link>
    );
  };

  const NavSection = ({ title, items }: { title: string; items: NavItem[] }) => (
    <div className="mb-6" role="navigation" aria-label={title}>
      {!isCollapsed && title && (
        <h3 className="mb-2 px-3 text-xs font-semibold text-[hsl(var(--muted-foreground))] uppercase tracking-wider">
          {title}
        </h3>
      )}
      <div className="space-y-1" role="menu">
        {items.map(item => (
          <NavItem key={item.name} item={item} />
        ))}
      </div>
    </div>
  );

  return (
    <aside
      className={cn(
        'flex flex-col border-r border-[hsl(var(--border))] bg-[hsl(var(--card))] shadow-md transition-all duration-300',
        isCollapsed ? 'w-16' : 'w-64 sm:w-72 lg:w-64',
        'h-full max-w-[90vw] sm:max-w-none'
      )}
    >
      <div className="flex items-center p-4 flex-shrink-0">
        {/* Mobile close button */}
        {setIsMobileMenuOpen && (
          <button
            onClick={() => setIsMobileMenuOpen(false)}
            className="lg:hidden rounded-md p-1.5 text-[hsl(var(--muted-foreground))] hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))] focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 focus:ring-offset-[hsl(var(--background))] mr-2"
            aria-label="Close menu"
          >
            <X className="h-4 w-4" />
          </button>
        )}

        {!isCollapsed && (
          <div className="flex-1 relative group">
            <div
              className="cursor-pointer select-none"
              onClick={() => {
                setClickCount(prev => {
                  const newCount = prev + 1;
                  if (newCount >= 5) {
                    // Use setTimeout to avoid setState during render
                    setTimeout(() => {
                      triggerUnlock();
                    }, 0);
                    return 0; // Reset counter
                  }
                  return newCount;
                });
              }}
            >
              <h2 className="font-semibold text-[hsl(var(--foreground))]">Thanmai Sai</h2>
              <p className="text-sm text-[hsl(var(--muted-foreground))]">
                Software Engineer
                {clickCount > 0 && clickCount < 5 && (
                  <span className="ml-2 text-xs opacity-60">
                    {Array.from({ length: clickCount }, () => '•').join('')}
                  </span>
                )}
              </p>
            </div>

            {/* Enhanced Tooltip */}
            {clickCount > 0 && clickCount < 5 && (
              <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 z-50 px-3 py-2 bg-[hsl(var(--popover))] border border-[hsl(var(--border))] rounded-lg shadow-lg text-xs text-[hsl(var(--popover-foreground))] whitespace-nowrap animate-in fade-in-0 zoom-in-95 duration-200">
                <div className="flex items-center gap-2">
                  <Gift className="h-4 w-4 text-[hsl(var(--primary))]" />
                  <span>{5 - clickCount} more clicks to unlock extras!</span>
                </div>
                {/* Arrow */}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-[hsl(var(--border))]"></div>
              </div>
            )}
          </div>
        )}

        {/* Collapse button - hidden on mobile */}
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              setIsCollapsed(!isCollapsed);
            }
          }}
          className="hidden lg:block rounded-md p-1.5 text-[hsl(var(--muted-foreground))] hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))] focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 focus:ring-offset-[hsl(var(--background))]"
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          aria-expanded={!isCollapsed}
          tabIndex={0}
        >
          {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </button>
      </div>

      <div className="flex-1 overflow-y-auto px-3 py-4">
        <NavSection title="Main" items={mainNavItems} />
        <NavSection title="Resources" items={resourcesNavItems} />
        {/* Extras section temporarily removed - can be restored by uncommenting extrasNavItems */}
        {/* <NavSection title="Extras" items={extrasNavItems} /> */}

        {/* Hidden easter egg section - discoverable through special interaction */}
        {isExtrasUnlocked && getHiddenExtrasNavItems(isExtrasUnlocked).length > 0 && (
          <NavSection title="Extras" items={getHiddenExtrasNavItems(isExtrasUnlocked)} />
        )}

        <NavSection title="Connect" items={connectNavItems} />

        <div className="mt-6 border-t border-[hsl(var(--border))] pt-4">
          {!isCollapsed ? (
            <>
              <div className="mb-2 px-2 text-xs font-semibold uppercase tracking-wider text-[hsl(var(--muted-foreground))]">
                Appearance
              </div>
              <div className="px-2">
                <ThemeSwitcher />
              </div>
            </>
          ) : (
            <div className="flex justify-center py-2">
              <button
                onClick={() => {
                  // Toggle through themes when collapsed
                  const themes: ThemeName[] = [
                    'dark',
                    'oceanic',
                    'midnight',
                    'synthwave',
                    'terminal',
                    'matrix',
                    'cyberpunk',
                  ];

                  // Get current theme from localStorage or default to "dark"
                  const currentTheme = (localStorage.getItem('theme') as ThemeName) || 'dark';
                  const currentIndex = themes.indexOf(currentTheme);

                  // Calculate next theme index, ensuring it's valid
                  const nextIndex =
                    currentIndex >= 0 && currentIndex < themes.length - 1 ? currentIndex + 1 : 0;
                  const nextTheme = themes[nextIndex];

                  // Remove all theme classes
                  document.documentElement.classList.remove(...themes.map(t => `theme-${t}`));

                  // Add the new theme class
                  document.documentElement.classList.add(`theme-${nextTheme}`);

                  // Save to localStorage
                  localStorage.setItem('theme', nextTheme);
                }}
                className="rounded-md p-1.5 text-[hsl(var(--muted-foreground))] hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))] focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 focus:ring-offset-[hsl(var(--background))]"
                aria-label="Cycle through themes"
                tabIndex={0}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    // Same theme cycling logic as the click handler
                    const themes: ThemeName[] = [
                      'dark',
                      'oceanic',
                      'midnight',
                      'synthwave',
                      'terminal',
                      'matrix',
                      'cyberpunk',
                    ];
                    const currentTheme = (localStorage.getItem('theme') as ThemeName) || 'dark';
                    const currentIndex = themes.indexOf(currentTheme);
                    const nextIndex =
                      currentIndex >= 0 && currentIndex < themes.length - 1 ? currentIndex + 1 : 0;
                    const nextTheme = themes[nextIndex];
                    document.documentElement.classList.remove(...themes.map(t => `theme-${t}`));
                    document.documentElement.classList.add(`theme-${nextTheme}`);
                    localStorage.setItem('theme', nextTheme);
                  }
                }}
              >
                <Palette className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="p-4 flex-shrink-0">
        <div className="relative">
          <input
            type="text"
            placeholder={isCollapsed ? '⌘K' : 'Search or Ask...'}
            className="w-full rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--input))] px-3 py-2 text-sm text-[hsl(var(--foreground))] placeholder-[hsl(var(--muted-foreground))] focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2"
            disabled
          />
          {!isCollapsed && (
            <kbd className="absolute right-3 top-1/2 -translate-y-1/2 inline-flex h-5 select-none items-center justify-center rounded border border-[hsl(var(--border))] bg-[hsl(var(--secondary))] px-1.5 font-mono text-[10px] font-medium text-[hsl(var(--muted-foreground))]">
              ⌘K
            </kbd>
          )}
        </div>
      </div>
    </aside>
  );
}
