"use client";

import { ReactNode, useState, useEffect } from "react";
import dynamic from "next/dynamic";

// Components
import { Sidebar, Footer } from "@/components/layout";
import { TabFocusManager } from "@/components/a11y";
import { PageLoader, PreloadBlogData, PreloadTechStackData } from "@/components/common";

// Dynamically import components that aren't needed for initial render
// These components are loaded only when needed to reduce initial bundle size
const CommandMenu = dynamic(() => import("@/components/features/CommandMenu"), {
  ssr: false,
  loading: () => null,
});

const KeyboardShortcutsHelp = dynamic(() => import("@/components/features/KeyboardShortcutsHelp"), {
  ssr: false,
  loading: () => null,
});

const VoiceAssistantBot = dynamic(() => import("@/components/features/VoiceAssistantBot"), {
  ssr: false,
  loading: () => null,
});

const ExtrasModalWrapper = dynamic(() => import("@/components/layout/ExtrasModalWrapper"), {
  ssr: false,
  loading: () => null,
});

// Preload these components after initial render for better UX
const preloadComponents = () => {
  import("@/components/features/CommandMenu");
  import("@/components/features/KeyboardShortcutsHelp");
  import("@/components/layout/ExtrasModalWrapper");
};

// Theme Provider
import { ThemeProvider } from "@/contexts/ThemeContext";
import { ExtrasProvider } from "@/contexts/ExtrasContext";

// Hooks
import { useFocusManagement } from "@/hooks/useFocusManagement";
import { useScrollBehavior } from "@/hooks/useScrollBehavior";
import { usePageLoader } from "@/hooks/usePageLoader";

interface RootLayoutClientProps {
  children: ReactNode;
}

export default function RootLayoutClient({ children }: RootLayoutClientProps) {
  const [showContent, setShowContent] = useState(false);
  const [isPageReady, setIsPageReady] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  const { isLoading, handleLoadingComplete } = usePageLoader();

  // Handle loading completion with slide animation
  const handleLoadingCompleteWithAnimation = () => {
    handleLoadingComplete();
    setIsPageReady(true);
    setTimeout(() => {
      setShowContent(true);
    }, 200);
  };

  // If no loader is shown, show content with animation
  useEffect(() => {
    if (!isLoading) {
      setIsPageReady(true);
      setTimeout(() => {
        setShowContent(true);
      }, 300);
    }
  }, [isLoading]);

  // Initialize focus management
  useFocusManagement();
  
  // Initialize scroll behavior
  useScrollBehavior();

  // Preload dynamic components for better performance
  useEffect(() => {
    const timer = setTimeout(() => {
      preloadComponents();
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  // Handle mobile menu body class
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.classList.add('menu-open');
    } else {
      document.body.classList.remove('menu-open');
    }
    
    // Cleanup on unmount
    return () => {
      document.body.classList.remove('menu-open');
    };
  }, [isMobileMenuOpen]);

  // Show loader on first visit
  if (isLoading) {
    return <PageLoader onLoadingComplete={handleLoadingCompleteWithAnimation} />;
  }

  return (
    <ThemeProvider>
      <ExtrasProvider>
        {/* Preload blog data for ultra-fast loading */}
        <PreloadBlogData />
        <PreloadTechStackData />

        {/* Tab focus manager to maintain tab position */}
        <TabFocusManager />

        {/* Skip to content link for keyboard users */}
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-[hsl(var(--primary))] focus:text-[hsl(var(--primary-foreground))] focus:rounded-md"
        >
          Skip to content
        </a>

        {/* Only show content after page is ready */}
        {isPageReady && (
        <div
          className={`viewport-height transition-all duration-1200 ease-out ${
            showContent
              ? 'transform translate-x-0 opacity-100'
              : 'transform translate-x-16 opacity-0'
          }`}
        >
          {/* Mobile menu overlay */}
          {isMobileMenuOpen && (
            <div
              className="fixed inset-0 bg-black/50 z-30 lg:hidden mobile-overlay"
              onClick={() => setIsMobileMenuOpen(false)}
              aria-hidden="true"
            />
          )}

          <div className="flex h-full overflow-hidden responsive-layout">
            {/* Sidebar with responsive behavior */}
            <div
              className={`sidebar-container transition-all duration-300 ease-out ${
                showContent
                  ? 'transform translate-x-0 opacity-100'
                  : 'transform -translate-x-16 opacity-0'
              } ${
                isMobileMenuOpen
                  ? 'fixed inset-y-0 left-0 z-40 lg:relative lg:translate-x-0'
                  : 'hidden lg:block'
              }`}
            >
              <Sidebar
                setIsMobileMenuOpen={setIsMobileMenuOpen}
              />
            </div>

            {/* Main content area */}
            <div
              className={`main-content-container flex flex-col h-full overflow-hidden transition-all duration-1200 ease-out delay-500 ${
                showContent
                  ? 'transform translate-x-0 opacity-100'
                  : 'transform translate-x-20 opacity-0'
              }`}
            >
              {/* Mobile header with menu button */}
              <div className="lg:hidden flex items-center justify-between p-4 border-b border-[hsl(var(--border))] bg-[hsl(var(--card))] sticky top-0 z-20 flex-shrink-0">
                <button
                  onClick={() => setIsMobileMenuOpen(true)}
                  className="p-2 rounded-md hover:bg-[hsl(var(--accent))] focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] transition-colors"
                  aria-label="Open menu"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
                <h1 className="font-semibold text-[hsl(var(--foreground))] text-lg">Thanmai Sai</h1>
                <div className="w-10" /> {/* Spacer for centering */}
              </div>

              {/* Scrollable content area */}
              <div className="flex-1 scrollable-area flex flex-col">
                <main id="main-content" className="flex-1 p-4 sm:p-6 lg:p-10 max-w-7xl mx-auto w-full" tabIndex={-1}>
                  {children}
                </main>
                <Footer />
              </div>
            </div>
          </div>
        </div>
        )}

        <CommandMenu />
        <KeyboardShortcutsHelp />
        <VoiceAssistantBot />
        <ExtrasModalWrapper />
      </ExtrasProvider>
    </ThemeProvider>
  );
}
