'use client';

import { useEffect, useRef } from 'react';

/**
 * Component that manages tab focus throughout the application
 */
export default function TabFocusManager() {
  // Store focus history for maintaining tab position
  const focusHistoryRef = useRef<string[]>([]);

  useEffect(() => {
    // Get all focusable elements
    const getFocusableElements = () => {
      return Array.from(
        document.querySelectorAll<HTMLElement>(
          'a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
        )
      );
    };

    // Ensure elements have IDs for tracking
    const ensureIds = () => {
      getFocusableElements().forEach((el, index) => {
        if (!el.id) {
          el.id = `focusable-element-${index}`;
        }
      });
    };

    // Track focus changes
    const handleFocusIn = (e: FocusEvent) => {
      if (!(e.target instanceof HTMLElement) || !e.target.id) return;

      // Update focus history (add to beginning, remove duplicates)
      const targetId = e.target.id;
      focusHistoryRef.current = [
        targetId,
        ...focusHistoryRef.current.filter(id => id !== targetId).slice(0, 4),
      ];

      // Store in sessionStorage for persistence
      sessionStorage.setItem('focusHistory', JSON.stringify(focusHistoryRef.current));
    };

    // Handle tab key navigation
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      // Only handle when focus is on body/document or no active element
      const activeElement = document.activeElement;
      if (activeElement && activeElement !== document.body && activeElement.tagName !== 'HTML')
        return;
      if (focusHistoryRef.current.length === 0) return;

      // Prevent default only when we're actually going to handle the focus
      const elementId =
        e.shiftKey && focusHistoryRef.current.length > 1
          ? focusHistoryRef.current[1] // Previous element for Shift+Tab
          : focusHistoryRef.current[0]; // Last focused element for Tab

      if (!elementId) return;

      const targetElement = document.getElementById(elementId);
      if (!targetElement) return;

      e.preventDefault();

      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
        return;
      }

      // Fallback: focus first/last element
      const focusableElements = getFocusableElements();
      if (focusableElements.length > 0) {
        const elementToFocus = e.shiftKey
          ? focusableElements[focusableElements.length - 1]
          : focusableElements[0];

        if (elementToFocus) {
          elementToFocus.focus();
        }
      }
    };

    // Initialize
    ensureIds();

    // Restore focus history from sessionStorage
    const storedHistory = sessionStorage.getItem('focusHistory');
    if (storedHistory) {
      try {
        focusHistoryRef.current = JSON.parse(storedHistory);
      } catch {
        // Invalid JSON, ignore
      }
    }

    // Add event listeners
    document.addEventListener('focusin', handleFocusIn);
    document.addEventListener('keydown', handleKeyDown, true);

    // Observer for DOM changes to ensure new elements have IDs
    const observer = new MutationObserver(mutations => {
      if (mutations.some(mutation => mutation.addedNodes.length > 0)) {
        ensureIds();
      }
    });

    observer.observe(document.body, { childList: true, subtree: true });

    // Clean up
    return () => {
      document.removeEventListener('focusin', handleFocusIn);
      document.removeEventListener('keydown', handleKeyDown, true);
      observer.disconnect();
    };
  }, []);

  return null; // This component doesn't render anything
}
