'use client';

import { useEffect, useRef } from 'react';

interface LiveRegionProps {
  message: string;
  assertive?: boolean;
  clearAfter?: number; // milliseconds
}

/**
 * Component that announces messages to screen readers
 * Uses ARIA live regions to make dynamic content accessible
 */
export default function LiveRegion({
  message,
  assertive = false,
  clearAfter = 5000,
}: LiveRegionProps) {
  const regionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!regionRef.current || !message) return undefined;

    // Set the message
    regionRef.current.textContent = message;

    // Clear the message after the specified time
    if (clearAfter > 0) {
      const timer = setTimeout(() => {
        if (regionRef.current) {
          regionRef.current.textContent = '';
        }
      }, clearAfter);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [message, clearAfter]);

  return (
    <div
      ref={regionRef}
      className="sr-only"
      aria-live={assertive ? 'assertive' : 'polite'}
      aria-atomic="true"
    >
      {message}
    </div>
  );
}
