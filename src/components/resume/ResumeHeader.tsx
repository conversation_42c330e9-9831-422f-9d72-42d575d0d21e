"use client";

import { Download, Printer } from "lucide-react";
import DownloadButton from "@/components/resume/DownloadButton";
import PrintButton from "@/components/resume/PrintButton";

export default function ResumeHeader() {
  return (
    <section className="space-y-4 sm:space-y-6">
      <div className="flex flex-col gap-4 sm:gap-6 lg:flex-row lg:items-center lg:justify-between">
        <div className="flex-1">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tight">
            Resume
          </h1>
          <p className="text-sm sm:text-base lg:text-lg text-[hsl(var(--muted-foreground))] max-w-[700px] mt-2">
            Professional resume showcasing my experience as a Generative AI Engineer and Full-Stack Developer.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 lg:flex-shrink-0">
          <DownloadButton
            variant="primary"
            size="md"
            icon={<Download className="w-4 h-4" />}
            className="w-full sm:w-auto"
          >
            Download PDF
          </DownloadButton>
          <PrintButton
            variant="outline"
            size="md"
            icon={<Printer className="w-4 h-4" />}
            className="w-full sm:w-auto"
          >
            Print PDF
          </PrintButton>
        </div>
      </div>
    </section>
  );
}
