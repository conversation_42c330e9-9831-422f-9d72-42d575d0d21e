'use client';

import { Button } from '@/components/ui';
import { CheckCircle, Printer } from 'lucide-react';
import { ReactNode, useState } from 'react';

interface PrintButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  icon?: ReactNode;
  className?: string;
  onClick?: () => void;
}

export default function PrintButton({
  children,
  variant = 'outline',
  size = 'md',
  icon,
  className,
  onClick,
}: PrintButtonProps) {
  const [isPrinting, setIsPrinting] = useState(false);
  const [printComplete, setPrintComplete] = useState(false);

  const handlePrint = () => {
    setIsPrinting(true);

    try {
      const printWindow = window.open('/Thanmai_Resume.pdf', '_blank');

      if (printWindow) {
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            setIsPrinting(false);
            setPrintComplete(true);
            setTimeout(() => setPrintComplete(false), 2000);
          }, 500);
        };

        // Fallback timeout
        setTimeout(() => {
          if (isPrinting) {
            printWindow.print();
            setIsPrinting(false);
            setPrintComplete(true);
            setTimeout(() => setPrintComplete(false), 2000);
          }
        }, 1000);
      } else {
        window.open('/Thanmai_Resume.pdf', '_blank');
        setIsPrinting(false);
      }

      onClick?.();
    } catch {
      setIsPrinting(false);
      window.open('/Thanmai_Resume.pdf', '_blank');
    }
  };

  const getButtonContent = () => {
    if (printComplete) {
      return (
        <>
          <CheckCircle className="w-4 h-4 mr-2" />
          Print Ready!
        </>
      );
    }

    if (isPrinting) {
      return (
        <>
          <Printer className="w-4 h-4 mr-2 animate-pulse" />
          Opening PDF...
        </>
      );
    }

    return (
      <>
        {icon && <span className="mr-2">{icon}</span>}
        {children}
      </>
    );
  };

  return (
    <Button
      onClick={handlePrint}
      variant={printComplete ? 'secondary' : variant}
      size={size}
      className={className}
      disabled={isPrinting}
    >
      {getButtonContent()}
    </Button>
  );
}
