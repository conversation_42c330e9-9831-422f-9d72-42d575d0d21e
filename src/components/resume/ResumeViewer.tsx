"use client";

import { Mail, Phone, MapPin, Globe, ExternalLink } from "lucide-react";

// Resume data constants
const PERSONAL_INFO = {
  name: "<PERSON><PERSON><PERSON>",
  title: "Generative AI Engineer & Full-Stack Developer",
  email: "<EMAIL>",
  phone: "+91 XXXXX XXXXX",
  location: "India",
  website: "portfolio.thanmaisai.dev",
  github: "github.com/thanmaisai",
  linkedin: "linkedin.com/in/thanmaisai"
};

const SKILLS = {
  "Programming Languages": ["Python", "TypeScript", "JavaScript", "Java", "SQL"],
  "AI/ML Technologies": ["LangChain", "OpenAI", "Hugging Face", "TensorFlow", "PyTorch", "RAG"],
  "Web Technologies": ["React", "Next.js", "Node.js", "Express", "FastAPI", "REST APIs"],
  "Tools & Platforms": ["Docker", "AWS", "Git", "PostgreSQL", "MongoDB", "Redis"]
};

const PROJECTS = [
  {
    title: "AI-Powered Portfolio Website",
    description: "Modern portfolio website with integrated AI features, voice assistant, and dynamic theming.",
    technologies: ["Next.js", "TypeScript", "Tailwind CSS", "AI Integration"]
  },
  {
    title: "Data Pipeline Optimization System",
    description: "Real-time data processing system with monitoring and alerting capabilities.",
    technologies: ["Python", "Apache Kafka", "PostgreSQL", "Docker"]
  }
];

export default function ResumeViewer() {
  return (
    <div className="w-full max-w-4xl mx-auto bg-[hsl(var(--card))] border border-[hsl(var(--border))] rounded-lg shadow-sm print:shadow-none print:border-none print:bg-white print:max-w-none transition-all duration-300 hover:shadow-md print:hover:shadow-none">
      {/* Header Section */}
      <div className="p-4 sm:p-6 lg:p-8 border-b border-[hsl(var(--border))] print:border-gray-300 print:break-inside-avoid">
        <div className="text-center space-y-3 sm:space-y-4">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight text-[hsl(var(--foreground))] print:text-black print:text-3xl">
            {PERSONAL_INFO.name}
          </h1>
          <p className="text-base sm:text-lg font-medium text-[hsl(var(--muted-foreground))] print:text-gray-700 print:text-base">
            {PERSONAL_INFO.title}
          </p>

          {/* Contact Information */}
          <div className="flex flex-col sm:flex-row sm:justify-center gap-2 sm:gap-4 lg:gap-6 text-sm text-[hsl(var(--muted-foreground))] print:text-gray-600 print:gap-3">
            <div className="flex items-center justify-center gap-2">
              <Mail className="w-4 h-4 print:w-3 print:h-3 flex-shrink-0" />
              <span className="break-all sm:break-normal">{PERSONAL_INFO.email}</span>
            </div>
            <div className="flex items-center justify-center gap-2">
              <Phone className="w-4 h-4 print:w-3 print:h-3 flex-shrink-0" />
              <span>{PERSONAL_INFO.phone}</span>
            </div>
            <div className="flex items-center justify-center gap-2">
              <MapPin className="w-4 h-4 print:w-3 print:h-3 flex-shrink-0" />
              <span>{PERSONAL_INFO.location}</span>
            </div>
          </div>

          {/* Social Links */}
          <div className="flex flex-col sm:flex-row sm:justify-center gap-2 sm:gap-4 text-sm print:gap-3">
            <div className="flex items-center justify-center gap-2 text-[hsl(var(--primary))] print:text-gray-700">
              <Globe className="w-4 h-4 print:w-3 print:h-3 flex-shrink-0" />
              <span className="break-all sm:break-normal">{PERSONAL_INFO.website}</span>
            </div>
            <div className="flex items-center justify-center gap-2 text-[hsl(var(--primary))] print:text-gray-700">
              <ExternalLink className="w-4 h-4 print:w-3 print:h-3 flex-shrink-0" />
              <span className="break-all sm:break-normal">{PERSONAL_INFO.github}</span>
            </div>
            <div className="flex items-center justify-center gap-2 text-[hsl(var(--primary))] print:text-gray-700">
              <ExternalLink className="w-4 h-4 print:w-3 print:h-3 flex-shrink-0" />
              <span className="break-all sm:break-normal">{PERSONAL_INFO.linkedin}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8 print:p-6 print:space-y-6">
        {/* Professional Summary */}
        <section className="print:break-inside-avoid">
          <h2 className="text-lg sm:text-xl font-bold text-[hsl(var(--foreground))] mb-3 sm:mb-4 border-b border-[hsl(var(--border))] pb-2 print:text-black print:border-gray-300 print:text-lg">
            Professional Summary
          </h2>
          <p className="text-sm sm:text-base text-[hsl(var(--muted-foreground))] leading-relaxed print:text-gray-700 print:leading-normal">
            Experienced Generative AI Engineer and Full-Stack Developer with expertise in modern web technologies,
            machine learning, and AI-driven solutions. Proven track record in developing scalable applications,
            implementing AI/ML models, and delivering high-quality software solutions. Passionate about leveraging
            cutting-edge technologies to solve complex problems and drive innovation.
          </p>
        </section>

        {/* Work Experience */}
        <section className="print:break-inside-avoid">
          <h2 className="text-lg sm:text-xl font-bold text-[hsl(var(--foreground))] mb-3 sm:mb-4 border-b border-[hsl(var(--border))] pb-2 print:text-black print:border-gray-300 print:text-lg">
            Work Experience
          </h2>

          <div className="space-y-4 sm:space-y-6 print:space-y-4">
            {/* Current Role */}
            <div className="print:break-inside-avoid">
              <div className="flex flex-col gap-1 sm:gap-2 mb-2 sm:mb-3 print:mb-2">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-1 sm:gap-2">
                  <div>
                    <h3 className="text-base sm:text-lg font-semibold text-[hsl(var(--foreground))] print:text-black print:text-base">
                      Generative AI Engineer
                    </h3>
                    <p className="text-sm sm:text-base text-[hsl(var(--primary))] font-medium print:text-gray-700">Ascendion</p>
                  </div>
                  <span className="text-xs sm:text-sm text-[hsl(var(--muted-foreground))] font-medium print:text-gray-600 sm:text-right">
                    June 2024 - Present
                  </span>
                </div>
              </div>
              <ul className="list-disc list-inside space-y-1 sm:space-y-2 text-sm sm:text-base text-[hsl(var(--muted-foreground))] ml-3 sm:ml-4 print:text-gray-700 print:space-y-1 print:ml-3">
                <li>Developed and deployed generative AI solutions using state-of-the-art language models</li>
                <li>Implemented RAG (Retrieval-Augmented Generation) systems for enhanced AI responses</li>
                <li>Built scalable AI pipelines and microservices architecture</li>
                <li>Collaborated with cross-functional teams to integrate AI capabilities into existing products</li>
                <li>Optimized model performance and reduced inference costs by 30%</li>
              </ul>
            </div>

            {/* Previous Role */}
            <div className="print:break-inside-avoid">
              <div className="flex flex-col gap-1 sm:gap-2 mb-2 sm:mb-3 print:mb-2">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-1 sm:gap-2">
                  <div>
                    <h3 className="text-base sm:text-lg font-semibold text-[hsl(var(--foreground))] print:text-black print:text-base">
                      Technical Intern
                    </h3>
                    <p className="text-sm sm:text-base text-[hsl(var(--primary))] font-medium print:text-gray-700">Hevo Data</p>
                  </div>
                  <span className="text-xs sm:text-sm text-[hsl(var(--muted-foreground))] font-medium print:text-gray-600 sm:text-right">
                    July 2023 - October 2023
                  </span>
                </div>
              </div>
              <ul className="list-disc list-inside space-y-1 sm:space-y-2 text-sm sm:text-base text-[hsl(var(--muted-foreground))] ml-3 sm:ml-4 print:text-gray-700 print:space-y-1 print:ml-3">
                <li>Developed data pipeline solutions for real-time data processing</li>
                <li>Implemented ETL processes using Python and modern data engineering tools</li>
                <li>Built monitoring and alerting systems for data pipeline reliability</li>
                <li>Contributed to the development of data transformation features</li>
                <li>Improved data processing efficiency by 25% through optimization techniques</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Technical Skills */}
        <section className="print:break-inside-avoid">
          <h2 className="text-lg sm:text-xl font-bold text-[hsl(var(--foreground))] mb-3 sm:mb-4 border-b border-[hsl(var(--border))] pb-2 print:text-black print:border-gray-300">
            Technical Skills
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 print:gap-4">
            {Object.entries(SKILLS).map(([category, skills]) => (
              <div key={category}>
                <h3 className="text-sm sm:text-base font-semibold text-[hsl(var(--foreground))] mb-2 sm:mb-3 print:text-black print:mb-2">
                  {category}
                </h3>
                <div className="flex flex-wrap gap-1.5 sm:gap-2 print:gap-1">
                  {skills.map((skill) => (
                    <span
                      key={skill}
                      className="inline-flex items-center rounded-md bg-[hsl(var(--secondary))] px-2 sm:px-2.5 py-0.5 sm:py-1 text-xs font-medium text-[hsl(var(--secondary-foreground))] print:bg-gray-100 print:text-black print:px-2 print:py-0.5"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Education */}
        <section className="print:break-inside-avoid">
          <h2 className="text-lg sm:text-xl font-bold text-[hsl(var(--foreground))] mb-3 sm:mb-4 border-b border-[hsl(var(--border))] pb-2 print:text-black print:border-gray-300 print:text-lg">
            Education
          </h2>

          <div>
            <div className="flex flex-col gap-1 sm:gap-2 mb-2">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-1 sm:gap-2">
                <div>
                  <h3 className="text-base sm:text-lg font-semibold text-[hsl(var(--foreground))] print:text-black print:text-base">
                    Bachelor of Technology in Computer Science
                  </h3>
                  <p className="text-sm sm:text-base text-[hsl(var(--primary))] font-medium print:text-gray-700">University Name</p>
                </div>
                <span className="text-xs sm:text-sm text-[hsl(var(--muted-foreground))] font-medium print:text-gray-600 sm:text-right">
                  2020 - 2024
                </span>
              </div>
            </div>
            <p className="text-sm sm:text-base text-[hsl(var(--muted-foreground))] print:text-gray-700">
              Relevant Coursework: Data Structures, Algorithms, Machine Learning, Database Systems, Software Engineering
            </p>
          </div>
        </section>

        {/* Key Projects */}
        <section className="print:break-inside-avoid">
          <h2 className="text-lg sm:text-xl font-bold text-[hsl(var(--foreground))] mb-3 sm:mb-4 border-b border-[hsl(var(--border))] pb-2 print:text-black print:border-gray-300 print:text-lg">
            Key Projects
          </h2>

          <div className="space-y-3 sm:space-y-4 print:space-y-3">
            {PROJECTS.map((project) => (
              <div key={project.title} className="print:break-inside-avoid">
                <h3 className="text-sm sm:text-base font-semibold text-[hsl(var(--foreground))] mb-1 sm:mb-2 print:text-black print:mb-1">
                  {project.title}
                </h3>
                <p className="text-sm sm:text-base text-[hsl(var(--muted-foreground))] mb-2 print:text-gray-700 print:mb-1">
                  {project.description}
                </p>
                <div className="flex flex-wrap gap-1.5 sm:gap-2 print:gap-1">
                  {project.technologies.map((tech) => (
                    <span
                      key={tech}
                      className="inline-flex items-center rounded-md bg-[hsl(var(--accent))] px-1.5 sm:px-2 py-0.5 sm:py-1 text-xs font-medium text-[hsl(var(--accent-foreground))] print:bg-gray-100 print:text-black print:px-1.5 print:py-0.5"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}
