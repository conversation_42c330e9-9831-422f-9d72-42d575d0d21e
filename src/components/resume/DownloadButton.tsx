'use client';

import { Button } from '@/components/ui';
import { CheckCircle, Download } from 'lucide-react';
import { ReactNode, useState } from 'react';

interface DownloadButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  icon?: ReactNode;
  className?: string;
  onClick?: () => void;
}

export default function DownloadButton({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  className,
  onClick,
}: DownloadButtonProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadComplete, setDownloadComplete] = useState(false);

  const handleDownload = async () => {
    setIsDownloading(true);

    try {
      const response = await fetch('/api/resume/download');
      const blob = await response.blob();

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'Thanmai_Sai_Resume.pdf';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);

      setIsDownloading(false);
      setDownloadComplete(true);

      setTimeout(() => setDownloadComplete(false), 2000);

      onClick?.();
    } catch (error) {
      console.error('Download failed:', error);
      setIsDownloading(false);

      // Fallback: direct link
      const link = document.createElement('a');
      link.href = '/Thanmai_Resume.pdf';
      link.download = 'Thanmai_Sai_Resume.pdf';
      link.click();
    }
  };

  const getButtonContent = () => {
    if (downloadComplete) {
      return (
        <>
          <CheckCircle className="w-4 h-4 mr-2" />
          Downloaded!
        </>
      );
    }

    if (isDownloading) {
      return (
        <>
          <Download className="w-4 h-4 mr-2 animate-bounce" />
          Downloading...
        </>
      );
    }

    return (
      <>
        {icon && <span className="mr-2">{icon}</span>}
        {children}
      </>
    );
  };

  return (
    <Button
      onClick={handleDownload}
      variant={downloadComplete ? 'secondary' : variant}
      size={size}
      className={className}
      disabled={isDownloading}
    >
      {getButtonContent()}
    </Button>
  );
}
