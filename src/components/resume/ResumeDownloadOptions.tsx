"use client";

import { Eye, FileText, Printer } from "lucide-react";
import DownloadButton from "@/components/resume/DownloadButton";
import PrintButton from "@/components/resume/PrintButton";

interface OptionCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  children: React.ReactNode;
}

const OptionCard = ({ icon, title, description, children }: OptionCardProps) => (
  <div className="rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] p-4 sm:p-6 transition-colors hover:bg-[hsl(var(--accent))] hover:border-[hsl(var(--accent-foreground))]">
    <div className="flex items-center gap-2 sm:gap-3 mb-3">
      {icon}
      <h3 className="font-medium text-base sm:text-lg">{title}</h3>
    </div>
    <p className="text-xs sm:text-sm text-[hsl(var(--muted-foreground))] mb-3 sm:mb-4">
      {description}
    </p>
    {children}
  </div>
);

export default function ResumeDownloadOptions() {
  return (
    <section className="space-y-4 sm:space-y-6">
      <h2 className="text-lg sm:text-xl lg:text-2xl font-bold tracking-tight">
        Download Options
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        <OptionCard
          icon={<FileText className="w-4 sm:w-5 h-4 sm:h-5 text-[hsl(var(--primary))] flex-shrink-0" />}
          title="PDF Format"
          description="Standard PDF resume perfect for applications and ATS systems."
        >
          <DownloadButton variant="secondary" size="sm" className="w-full sm:w-auto">
            Download PDF
          </DownloadButton>
        </OptionCard>

        <OptionCard
          icon={<Eye className="w-4 sm:w-5 h-4 sm:h-5 text-[hsl(var(--primary))] flex-shrink-0" />}
          title="View Online"
          description="Interactive web version with enhanced readability and navigation."
        >
          <button className="w-full sm:w-auto inline-flex h-8 items-center justify-center rounded-md bg-[hsl(var(--secondary))] px-3 sm:px-4 text-xs sm:text-sm font-medium text-[hsl(var(--secondary-foreground))] transition-colors hover:bg-[hsl(var(--secondary))]/80">
            You're viewing it!
          </button>
        </OptionCard>

        <OptionCard
          icon={<Printer className="w-4 sm:w-5 h-4 sm:h-5 text-[hsl(var(--primary))] flex-shrink-0" />}
          title="Print PDF"
          description="Open PDF in new tab for printing with original formatting."
        >
          <PrintButton variant="secondary" size="sm" className="w-full sm:w-auto">
            Print PDF
          </PrintButton>
        </OptionCard>
      </div>
    </section>
  );
}
