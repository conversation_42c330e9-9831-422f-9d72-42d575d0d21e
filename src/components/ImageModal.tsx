'use client';

import { ChevronLeft, ChevronRight, X } from 'lucide-react';
import { useEffect, useState } from 'react';

interface ImageModalProps {
  src: string | string[];
  alt: string;
}

export function ImageModal({ src, alt }: ImageModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  // Convert single image to array for consistent handling
  const images = Array.isArray(src) ? src : [src];

  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);

  // Handle keyboard navigation
  const handleKeyDown = (e: KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowLeft':
        setCurrentIndex(prev => (prev - 1 + images.length) % images.length);
        e.preventDefault();
        break;
      case 'ArrowRight':
        setCurrentIndex(prev => (prev + 1) % images.length);
        e.preventDefault();
        break;
      case 'Escape':
        closeModal();
        e.preventDefault();
        break;
    }
  };

  const nextImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex(prev => (prev + 1) % images.length);
  };

  const prevImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex(prev => (prev - 1 + images.length) % images.length);
  };

  // Add and remove keyboard event listener
  useEffect(() => {
    if (isOpen) {
      // Add event listener when modal is open
      window.addEventListener('keydown', handleKeyDown);

      // Return cleanup function to remove event listener
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isOpen, currentIndex]); // Re-run effect when modal state or current image changes

  return (
    <>
      <div className={`flex flex-wrap gap-1 ${images.length > 1 ? 'max-w-[140px]' : ''}`}>
        {images.map((imgSrc, index) => (
          <button
            key={index}
            onClick={() => {
              setCurrentIndex(index);
              openModal();
            }}
            className="block rounded overflow-hidden hover:opacity-90 transition-all duration-200 transform hover:scale-105"
            aria-label={`View ${alt} image ${index + 1} in full size`}
          >
            <img src={imgSrc} alt={`${alt} ${index + 1}`} className="w-10 h-10 object-cover" />
          </button>
        ))}
      </div>

      {isOpen && (
        <div
          className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-in fade-in duration-200"
          onClick={closeModal}
        >
          <div className="relative max-w-4xl max-h-[90vh] w-full bg-white/10 rounded-xl overflow-hidden">
            <button
              onClick={closeModal}
              className="absolute top-2 right-2 p-2 text-white/80 hover:text-white rounded-full bg-black/20 hover:bg-black/40 transition-colors"
              aria-label="Close modal (Escape key)"
              title="Close (Escape key)"
            >
              <X size={20} />
            </button>

            <img
              src={images[currentIndex]}
              alt={`${alt} ${currentIndex + 1}`}
              className="w-full h-auto max-h-[90vh] object-contain"
              onClick={e => e.stopPropagation()}
            />

            {images.length > 1 && (
              <div className="absolute inset-x-0 bottom-4 flex flex-col items-center">
                <div className="flex justify-center gap-3 items-center text-white/90 mb-2">
                  <button
                    onClick={prevImage}
                    className="p-2 rounded-full bg-black/30 hover:bg-black/50 transition-colors"
                    aria-label="Previous image (Left arrow key)"
                    title="Previous image (Left arrow key)"
                  >
                    <ChevronLeft size={16} />
                  </button>
                  <span className="text-xs font-light">
                    {currentIndex + 1} / {images.length}
                  </span>
                  <button
                    onClick={nextImage}
                    className="p-2 rounded-full bg-black/30 hover:bg-black/50 transition-colors"
                    aria-label="Next image (Right arrow key)"
                    title="Next image (Right arrow key)"
                  >
                    <ChevronRight size={16} />
                  </button>
                </div>
                <div className="text-xs text-white/70 hidden sm:block">
                  <span className="mx-1">←/→: Navigate</span>
                  <span className="mx-1">ESC: Close</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}
