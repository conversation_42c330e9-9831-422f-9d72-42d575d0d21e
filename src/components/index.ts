/**
 * Components Index
 * 
 * Master export file for all application components.
 * Provides organized access to components by category.
 * 
 * Usage:
 * ```tsx
 * import { <PERSON><PERSON>, Card } from "@/components/ui";
 * import { AnimatedBlogCard, NewBadge } from "@/components/blog";
 * import { PageLoader, ProjectCard } from "@/components/common";
 * ```
 */

// Re-export all component categories
export * as UI from "./ui";
export * as Blog from "./blog";
export * as Common from "./common";
export * as Features from "./features";
export * as Layout from "./layout";
export * as Resume from "./resume";
export * as A11y from "./a11y";

export * as Home from "./home";

// Direct exports for commonly used components
export { Button } from "./ui";
export { AnimatedBlogCard, NewBadge } from "./blog";
export { PageLoader, ProjectCard } from "./common";
export { CommandMenu, ThemeSwitcher } from "./features";
export { Sidebar, Footer } from "./layout";
