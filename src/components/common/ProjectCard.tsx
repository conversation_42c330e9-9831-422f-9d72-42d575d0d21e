'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui';
import { Project } from '@/types/content';
import Image from 'next/image';
import Link from 'next/link';

interface ProjectCardProps {
  project: Project;
}

export function ProjectCard({ project }: ProjectCardProps) {
  return (
    <Link href={`/projects/${project.slug}`}>
      <Card className="overflow-hidden transition-all duration-200 hover:border-[hsl(var(--accent))] hover:shadow-md">
        <div className="relative h-48 w-full">
          <Image src={project.imageUrl} alt={project.title} fill className="object-cover" />
        </div>
        <CardHeader>
          <CardTitle>{project.title}</CardTitle>
          <CardDescription>{project.shortDescription}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {project.tags.map(tag => (
              <span
                key={tag}
                className="rounded-full bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]"
              >
                {tag}
              </span>
            ))}
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
