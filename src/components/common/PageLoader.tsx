/**
 * PageLoader Component
 *
 * A sophisticated loading screen with smooth progress animation and stage-based messaging.
 * Provides visual feedback during application initialization and data loading.
 *
 * Features:
 * - Smooth progress animation with realistic timing
 * - Stage-based loading messages
 * - Configurable minimum load time
 * - Automatic fallback timeout
 * - Responsive design with theme integration
 *
 * @example
 * ```tsx
 * <PageLoader
 *   onLoadingComplete={() => setLoading(false)}
 *   config={{ minLoadTime: 800 }}
 * />
 * ```
 */

"use client";

import { useEffect, useState } from "react";

interface PageLoaderProps {
  onLoadingComplete: () => void;
  config?: {
    minLoadTime?: number;
  };
}

type LoadingStage = 'initializing' | 'loading' | 'finalizing';

export default function PageLoader({ onLoadingComplete, config = {} }: PageLoaderProps) {
  const [displayProgress, setDisplayProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [isDataReady, setIsDataReady] = useState(false);
  const [loadingStage, setLoadingStage] = useState<LoadingStage>('initializing');

  const minLoadTime = config.minLoadTime || 600;

  // Listen for data ready event with fallback timeout
  useEffect(() => {
    const handleDataReady = () => {
      setIsDataReady(true);
      setLoadingStage('finalizing');
    };

    window.addEventListener('homeDataReady', handleDataReady);

    // Fallback timeout to prevent infinite loading
    const fallbackTimeout = setTimeout(() => {
      setIsDataReady(true);
      setLoadingStage('finalizing');
    }, 3000); // Reduced to 3 seconds for faster experience

    return () => {
      window.removeEventListener('homeDataReady', handleDataReady);
      clearTimeout(fallbackTimeout);
    };
  }, []);

  // Smooth number increment without flickering
  useEffect(() => {
    const startTime = Date.now();

    const interval = setInterval(() => {
      setDisplayProgress(prev => {
        let increment: number;
        let maxProgress: number;

        if (loadingStage === 'initializing') {
          // Quick initial: 0,2,4,6,8,10...20,25,30
          maxProgress = 30;
          increment = prev < 10 ? 2 : prev < 20 ? 3 : prev < 25 ? 2 : 1;

          if (prev >= 25) {
            setLoadingStage('loading');
          }
        } else if (loadingStage === 'loading') {
          // Faster steady progress: 30,32,34...50,53,56...70,73,76...85
          maxProgress = isDataReady ? 85 : Math.min(85, 30 + Math.floor((Date.now() - startTime) / 60));
          increment = prev < 50 ? 2 : prev < 70 ? 3 : prev < 80 ? 2 : 1;
        } else {
          // Quick final phase: 85,88,91,94,97,100
          maxProgress = 100;
          increment = prev < 95 ? 3 : 2;
        }

        const newProgress = Math.min(Math.floor(prev + increment), maxProgress);

        // Complete loading
        if (newProgress >= 100) {
          const elapsedTime = Date.now() - startTime;
          const remainingTime = Math.max(minLoadTime - elapsedTime, 0);

          setTimeout(() => {
            setIsVisible(false);
            setTimeout(() => {
              onLoadingComplete();
            }, 300);
          }, remainingTime);
          return 100;
        }

        return newProgress;
      });
    }, 80); // Update every 80ms for faster, smooth increments

    return () => clearInterval(interval);
  }, [onLoadingComplete, minLoadTime, isDataReady, loadingStage]);

  if (!isVisible) return null;

  // Stage-based messages for better user feedback
  const getStageMessage = () => {
    switch (loadingStage) {
      case 'initializing':
        return 'Initializing...';
      case 'loading':
        return 'Loading content...';
      case 'finalizing':
        return 'Almost ready...';
      default:
        return '';
    }
  };

  return (
    <div
      className={`fixed inset-0 z-[9999] flex items-center justify-center transition-opacity duration-300 ${
        !isVisible ? 'opacity-0' : 'opacity-100'
      }`}
      style={{
        background: 'hsl(var(--background))',
      }}
    >
      <div className="flex flex-col items-center space-y-6">
        <div
          className={`relative w-20 h-20 transition-transform duration-200 ${
            loadingStage === 'loading' && displayProgress < 85 ? 'animate-pulse' : ''
          }`}
        >
          {/* Background circle */}
          <svg
            className="w-full h-full transform -rotate-90"
            viewBox="0 0 100 100"
          >
            <circle
              cx="50"
              cy="50"
              r="42"
              stroke="hsl(var(--border))"
              strokeWidth="1.5"
              fill="none"
              className="opacity-20"
            />
            {/* Progress circle with smooth animation */}
            <circle
              cx="50"
              cy="50"
              r="42"
              stroke="hsl(var(--primary))"
              strokeWidth="1.5"
              fill="none"
              strokeLinecap="round"
              strokeDasharray={`${2 * Math.PI * 42}`}
              strokeDashoffset={`${2 * Math.PI * 42 * (1 - displayProgress / 100)}`}
              className="transition-all duration-75 ease-out"
              style={{
                filter: loadingStage === 'finalizing' ? 'drop-shadow(0 0 6px hsl(var(--primary)))' : 'none'
              }}
            />
          </svg>

          {/* Center content with subtle animation */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div
                className="text-lg font-medium text-foreground transition-all duration-200"
                style={{
                  transform: loadingStage === 'finalizing' ? 'scale(1.05)' : 'scale(1)',
                }}
              >
                {displayProgress}%
              </div>
            </div>
          </div>
        </div>

        {/* Stage message with fade transition */}
        <div className="h-6 flex items-center justify-center">
          <p
            className="text-sm text-muted-foreground transition-all duration-300 ease-in-out"
            style={{
              opacity: getStageMessage() ? 1 : 0,
              transform: getStageMessage() ? 'translateY(0)' : 'translateY(4px)'
            }}
          >
            {getStageMessage()}
          </p>
        </div>
      </div>
    </div>
  );
}
