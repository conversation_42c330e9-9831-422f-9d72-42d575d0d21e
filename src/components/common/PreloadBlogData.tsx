"use client"

import { useEffect } from 'react'
import { preloadBlogData } from '@/lib/preload'

// Component that preloads blog data immediately when app starts
export default function PreloadBlogData() {
  useEffect(() => {
    // Start preloading immediately when component mounts
    preloadBlogData()
    
    // Preload again when user focuses the tab (in case they've been away)
    const handleFocus = () => {
      preloadBlogData()
    }
    
    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [])

  // This component renders nothing - it's just for preloading
  return null
}
