"use client"

import { useState } from 'react'
import Image from 'next/image'

interface FlippablePhotoProps {
  frontImageSrc: string
  backImageSrc: string
  altText: string
  className?: string
}

export default function FlippablePhoto({
  frontImageSrc,
  backImageSrc,
  altText,
  className = '',
}: FlippablePhotoProps) {
  const [isFlipped, setIsFlipped] = useState(false)

  const handleClick = () => {
    setIsFlipped(!isFlipped)
  }

  return (
    <div 
      className={`relative cursor-pointer ${className}`}
      style={{ 
        perspective: '1000px',
        height: '100%',
        width: '100%'
      }}
      onClick={handleClick}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleClick()
        }
      }}
      role="button"
      tabIndex={0}
      aria-label={`Click to flip photo of ${altText}`}
    >
      <div 
        className="relative w-full h-full transition-all duration-500"
        style={{ 
          transformStyle: 'preserve-3d',
          transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)',
        }}
      >
        {/* Front side */}
        <div 
          className="absolute w-full h-full backface-hidden"
          style={{ backfaceVisibility: 'hidden' }}
        >
          <Image
            src={frontImageSrc}
            alt={altText}
            fill
            className="object-cover rounded-lg"
            priority
          />
        </div>
        
        {/* Back side */}
        <div 
          className="absolute w-full h-full backface-hidden"
          style={{ 
            backfaceVisibility: 'hidden',
            transform: 'rotateY(180deg)'
          }}
        >
          <Image
            src={backImageSrc}
            alt={`Alternative view of ${altText}`}
            fill
            className="object-cover rounded-lg"
            priority
          />
        </div>
      </div>
    </div>
  )
}
