"use client"

import { useEffect } from 'react'
import { preloadTechStackData } from '@/lib/airtable/preloadTechStack'

/**
 * Component that preloads tech stack data when the app starts
 * This component renders nothing - it's just for preloading
 */
export default function PreloadTechStackData() {
  useEffect(() => {
    // Start preloading immediately when component mounts
    preloadTechStackData()
    
    // Preload again when user focuses the tab (in case they've been away)
    const handleFocus = () => {
      preloadTechStackData()
    }
    
    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [])

  // This component renders nothing - it's just for preloading
  return null
}
