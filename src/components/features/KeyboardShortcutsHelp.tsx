"use client";

import { useState, useRef, useEffect } from "react";
import { X } from "lucide-react";
import { useKeyboardShortcuts } from "@/hooks/useKeyboardShortcuts";
import { useExtras } from "@/contexts/ExtrasContext";

export default function KeyboardShortcutsHelp() {
  const [isOpen, setIsOpen] = useState(false);
  const closeButtonRef = useRef<HTMLButtonElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);
  const { isExtrasUnlocked } = useExtras();

  // Use keyboard shortcuts hook with empty setCommandOpen function
  useKeyboardShortcuts({
    setCommandOpen: () => {},
    setShortcutsHelpOpen: setIsOpen,
    isExtrasUnlocked
  });

  // Focus management for the dialog
  useEffect(() => {
    if (isOpen) {
      // Store the currently focused element before opening the dialog
      previousFocusRef.current = document.activeElement as HTMLElement;

      // Focus the close button when the dialog opens
      setTimeout(() => {
        closeButtonRef.current?.focus();
      }, 50);
    } else if (previousFocusRef.current) {
      // When closing, restore focus to the previously focused element
      previousFocusRef.current.focus();
    }
  }, [isOpen]);

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setIsOpen(true);
          }
        }}
        className="fixed bottom-4 right-4 z-10 rounded-full bg-[hsl(var(--primary))] p-2 text-[hsl(var(--primary-foreground))] shadow-lg hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))] focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 focus:ring-offset-[hsl(var(--background))]"
        aria-label="Keyboard Shortcuts"
        aria-haspopup="dialog"
        aria-expanded={isOpen}
      >
        <span className="font-mono font-bold text-lg">/</span>
      </button>

      {isOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              e.preventDefault();
              setIsOpen(false);
            }
          }}
        >
          <div
            className="relative max-h-[90vh] w-full max-w-2xl overflow-auto rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] p-6 shadow-lg"
            role="dialog"
            aria-modal="true"
            aria-labelledby="keyboard-shortcuts-title"
            tabIndex={-1}
          >
            <button
              ref={closeButtonRef}
              onClick={() => setIsOpen(false)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setIsOpen(false);
                }
              }}
              className="absolute right-4 top-4 rounded-full p-1 text-[hsl(var(--muted-foreground))] hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))] focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 focus:ring-offset-[hsl(var(--background))]"
              aria-label="Close keyboard shortcuts dialog"
            >
              <X className="h-5 w-5" />
            </button>

            <h2 id="keyboard-shortcuts-title" className="mb-4 text-xl font-bold">Keyboard Shortcuts</h2>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <h3 className="mb-2 font-medium">Navigation</h3>
                <ul className="space-y-2">
                  <li className="flex items-center justify-between">
                    <span>Home</span>
                    <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">1</kbd>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Experience</span>
                    <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">2</kbd>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Projects</span>
                    <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">3</kbd>
                  </li>
                  {/* Services temporarily commented out */}
                  {/* <li className="flex items-center justify-between">
                    <span>Services</span>
                    <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">4</kbd>
                  </li> */}
                  <li className="flex items-center justify-between">
                    <span>About</span>
                    <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">4</kbd>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Resume</span>
                    <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">5</kbd>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Updates</span>
                    <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">6</kbd>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Blog</span>
                    <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">7</kbd>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Tech Stack</span>
                    <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">8</kbd>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="mb-2 font-medium">Special Shortcuts</h3>
                <ul className="space-y-2">
                  <li className="flex items-center justify-between">
                    <span>Command Menu</span>
                    <div className="flex items-center gap-1">
                      <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">Ctrl</kbd>
                      <span>+</span>
                      <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">K</kbd>
                    </div>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Extras (Hidden)</span>
                    <div className="flex items-center gap-1">
                      <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">Ctrl</kbd>
                      <span>+</span>
                      <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">E</kbd>
                    </div>
                  </li>
                  <li className="flex items-center justify-between">
                    <span>Contact</span>
                    <div className="flex items-center gap-1">
                      <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">Ctrl</kbd>
                      <span>+</span>
                      <kbd className="rounded bg-[hsl(var(--secondary))] px-2 py-1 text-xs text-[hsl(var(--secondary-foreground))]">C</kbd>
                    </div>
                  </li>
                </ul>

                <h3 className="mb-2 mt-6 font-medium">Tips</h3>
                <ul className="space-y-2 text-sm text-[hsl(var(--muted-foreground))]">
                  <li>Shortcuts won't trigger when typing in text fields</li>
                  <li>Press <kbd className="rounded bg-[hsl(var(--secondary))] px-1 text-xs text-[hsl(var(--secondary-foreground))]">?</kbd> to show/hide this help</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
