"use client";

import { useRef, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON>, AlertCircle } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { useVoiceAssistant } from "@/hooks/useVoiceAssistant";
import LiveRegion from "@/components/a11y/LiveRegion";

export default function VoiceAssistantBot() {
  const {
    isOpen,
    isMuted,
    isListening,
    transcript,
    response,
    error,
    isInitialized,
    volumeLevel,
    isSpeaking,
    toggleAssistant,
    toggleMute,
    toggleListening,
    closeAssistant,
    setIsOpen
  } = useVoiceAssistant();

  // State for microphone permissions
  const [micPermission, setMicPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');

  const closeButtonRef = useRef<HTMLButtonElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);

  // Check microphone permissions
  const checkMicrophonePermission = async () => {
    try {
      // Only check if the browser supports the permissions API
      if (navigator.permissions && navigator.permissions.query) {
        const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        setMicPermission(permissionStatus.state as 'granted' | 'denied' | 'prompt');

        // Listen for permission changes
        permissionStatus.onchange = () => {
          setMicPermission(permissionStatus.state as 'granted' | 'denied' | 'prompt');
        };
      }
    } catch (err) {
      console.error('Error checking microphone permission:', err);
    }
  };

  // Handle assistant button click - only now we check for permissions
  const handleAssistantClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Check permissions when the user clicks the button
    checkMicrophonePermission();
    toggleAssistant();
  };

  // Wrapper functions to handle event propagation
  const handleToggleMute = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleMute();
  };

  const handleToggleListening = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Check permissions when the user tries to start listening
    checkMicrophonePermission();
    toggleListening();
  };

  // Handle ESC key to close the dialog
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, setIsOpen]);

  // Focus management for accessibility
  useEffect(() => {
    if (isOpen) {
      // Save the previously focused element
      previousFocusRef.current = document.activeElement as HTMLElement;
      // Focus the close button when dialog opens
      setTimeout(() => {
        closeButtonRef.current?.focus();
      }, 100);
    } else if (previousFocusRef.current) {
      // Restore focus when dialog closes
      previousFocusRef.current.focus();
    }
  }, [isOpen]);

  // State for accessibility announcements
  const [announcement, setAnnouncement] = useState("");

  // Update announcement when state changes
  useEffect(() => {
    if (error) {
      setAnnouncement(`Voice assistant error: ${error}`);
    } else if (micPermission === 'denied') {
      setAnnouncement("Microphone access denied. Please allow microphone access to use the voice assistant.");
    } else if (isListening) {
      setAnnouncement("Voice assistant is now listening");
    } else if (isSpeaking) {
      setAnnouncement("Voice assistant is speaking");
    } else if (transcript) {
      setAnnouncement(`You said: ${transcript}`);
    } else if (response) {
      setAnnouncement(`Assistant response: ${response}`);
    }
  }, [isListening, isSpeaking, transcript, response, error, micPermission]);

  return (
    <>
      {/* Bot trigger button - positioned in bottom-right corner */}
      <button
        onClick={handleAssistantClick}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            checkMicrophonePermission();
            toggleAssistant();
          }
        }}
        className="fixed bottom-4 right-16 z-10 rounded-full bg-[hsl(var(--primary))] p-2 text-[hsl(var(--primary-foreground))] shadow-sm hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))] focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 focus:ring-offset-[hsl(var(--background))]"
        aria-label="Voice Assistant"
        aria-haspopup="dialog"
        aria-expanded={isOpen}
        disabled={!isInitialized}
        title={isInitialized ? "Open Voice Assistant" : "Voice Assistant Unavailable"}
      >
        <Mic className="h-5 w-5" />
      </button>

      {/* Bot dialog */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed z-50 bottom-20 right-4 p-0 pointer-events-none flex items-end justify-end"
            onClick={() => setIsOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              transition={{ duration: 0.15, type: "spring", stiffness: 300, damping: 30 }}
              className="w-64 pointer-events-auto rounded-lg border border-[hsl(var(--border))] bg-[hsl(var(--card))] shadow-md overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Dialog header */}
              <div className="flex items-center justify-between border-b border-[hsl(var(--border))] px-3 py-2">
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "flex items-center justify-center w-6 h-6 rounded-full transition-colors",
                    isListening
                      ? "bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))]"
                      : isSpeaking
                        ? "bg-[hsl(var(--success))] text-[hsl(var(--success-foreground))]"
                        : "bg-[hsl(var(--muted))] text-[hsl(var(--muted-foreground))]"
                  )}>
                    {isMuted ? <MicOff className="h-3 w-3" /> : <Mic className="h-3 w-3" />}
                  </div>
                  <div>
                    <p className="text-xs font-medium">
                      {isListening
                        ? "Listening..."
                        : isSpeaking
                          ? "Speaking..."
                          : isMuted
                            ? "Mic muted"
                            : "Voice Assistant"}
                    </p>

                    {/* Volume level indicator */}
                    {(isListening || isSpeaking) && (
                      <div className="w-20 h-1 mt-1 bg-[hsl(var(--muted))] rounded-full overflow-hidden">
                        <motion.div
                          className="h-full bg-[hsl(var(--primary))]"
                          style={{ width: `${volumeLevel * 100}%` }}
                          animate={{ opacity: [0.7, 1, 0.7] }}
                          transition={{ duration: 1.5, repeat: Infinity }}
                        />
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex gap-1">
                  <button
                    onClick={handleToggleMute}
                    className="rounded-full p-1 text-[hsl(var(--muted-foreground))] hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))] focus:outline-none focus:ring-1 focus:ring-[hsl(var(--ring))]"
                    aria-label={isMuted ? "Unmute microphone" : "Mute microphone"}
                    aria-pressed={isMuted}
                  >
                    {isMuted ? <Mic className="h-3 w-3" /> : <MicOff className="h-3 w-3" />}
                  </button>
                  <button
                    ref={closeButtonRef}
                    onClick={closeAssistant}
                    className="rounded-full p-1 text-[hsl(var(--muted-foreground))] hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))] focus:outline-none focus:ring-1 focus:ring-[hsl(var(--ring))]"
                    aria-label="Close voice assistant"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              </div>

              {/* Dialog content */}
              <div className="p-3">
                {/* Microphone button with animation */}
                <button
                  onClick={handleToggleListening}
                  className={cn(
                    "relative flex items-center justify-center w-full h-10 rounded-md transition-all duration-300 focus:outline-none focus:ring-1 focus:ring-[hsl(var(--ring))]",
                    isListening
                      ? "bg-[hsl(var(--primary))]"
                      : "bg-[hsl(var(--secondary))]",
                    isMuted && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={isMuted}
                  aria-label={isListening ? "Stop listening" : "Start listening"}
                >
                  {/* Ripple effect when listening */}
                  {isListening && !isMuted && (
                    <motion.div
                      className="absolute inset-0 rounded-md bg-[hsl(var(--primary))]"
                      animate={{
                        opacity: [0.7, 0.5, 0.7]
                      }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    />
                  )}

                  <div className="flex items-center gap-2">
                    {isMuted ? (
                      <MicOff className="h-4 w-4 text-[hsl(var(--secondary-foreground))]" />
                    ) : (
                      <Mic className={cn(
                        "h-4 w-4",
                        isListening
                          ? "text-[hsl(var(--primary-foreground))]"
                          : "text-[hsl(var(--secondary-foreground))]"
                      )} />
                    )}
                    <span className={cn(
                      "text-sm",
                      isListening
                        ? "text-[hsl(var(--primary-foreground))]"
                        : "text-[hsl(var(--secondary-foreground))]"
                    )}>
                      {isListening ? "Tap to stop" : "Tap to speak"}
                    </span>
                  </div>
                </button>

                {/* Transcript and response area with improved design */}
                <div className="mt-3 rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--card))] overflow-hidden">
                  {/* Status bar showing mic permission */}
                  <div className="px-2 py-1 bg-[hsl(var(--secondary))] border-b border-[hsl(var(--border))] flex justify-between items-center">
                    <span className="text-xs font-medium">Conversation</span>
                    {micPermission === 'granted' ? (
                      <span className="text-xs text-[hsl(var(--success))]">Mic access granted</span>
                    ) : micPermission === 'denied' ? (
                      <span className="text-xs text-[hsl(var(--destructive))]">Mic access denied</span>
                    ) : (
                      <span className="text-xs text-[hsl(var(--muted-foreground))]">Waiting for mic permission</span>
                    )}
                  </div>

                  {/* Conversation content */}
                  <div className="p-2 max-h-[150px] overflow-y-auto">
                    {error && (
                      <div className="mb-2 text-xs flex items-start gap-1 text-[hsl(var(--destructive))] p-1.5 bg-[hsl(var(--destructive))/10] rounded">
                        <AlertCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                        <span>{error}</span>
                      </div>
                    )}

                    {!transcript && !response && !error && (
                      <div className="text-xs text-center text-[hsl(var(--muted-foreground))] py-4">
                        {isListening ? "Listening for your voice..." : "Tap the button below to start speaking"}
                      </div>
                    )}

                    {transcript && (
                      <div className="mb-2 text-xs p-1.5 bg-[hsl(var(--muted))/20] rounded-lg">
                        <div className="font-medium text-[hsl(var(--muted-foreground))] mb-1 flex items-center">
                          <span className="bg-[hsl(var(--muted))] text-[hsl(var(--muted-foreground))] rounded-full p-1 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                          </span>
                          You
                        </div>
                        <span className="text-[hsl(var(--foreground))]">{transcript}</span>
                      </div>
                    )}

                    {response && (
                      <div className="text-xs p-1.5 bg-[hsl(var(--primary))/10] rounded-lg">
                        <div className="font-medium text-[hsl(var(--primary))] mb-1 flex items-center">
                          <span className="bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-full p-1 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/><path d="m9 12 2 2 4-4"/></svg>
                          </span>
                          Assistant
                        </div>
                        <span className="text-[hsl(var(--foreground))]">{response}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Accessibility announcements */}
      <LiveRegion message={announcement} />
    </>
  );
}
