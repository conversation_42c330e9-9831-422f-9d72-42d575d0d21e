'use client';

import confetti from 'canvas-confetti';
import { AnimatePresence, motion } from 'framer-motion';
import { <PERSON>R<PERSON>, Lightbulb, PartyPopper, Sparkles, Star, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface ExtrasUnlockedModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ExtrasUnlockedModal({ isOpen, onClose }: ExtrasUnlockedModalProps) {
  const router = useRouter();

  // Trigger confetti animation when modal opens - from bottom corners only
  useEffect(() => {
    if (isOpen) {
      const colors = ['#a864fd', '#29cdff', '#78ff44', '#ff718d', '#fdff6a', '#efe5e1'];

      // Bottom left corner spray
      confetti({
        particleCount: 60,
        angle: 45,
        spread: 60,
        origin: { x: 0.1, y: 0.9 },
        colors,
        startVelocity: 45,
        gravity: 0.8,
        scalar: 0.8,
      });

      // Bottom right corner spray
      setTimeout(() => {
        confetti({
          particleCount: 60,
          angle: 135,
          spread: 60,
          origin: { x: 0.9, y: 0.9 },
          colors,
          startVelocity: 45,
          gravity: 0.8,
          scalar: 0.8,
        });
      }, 150);

      // Additional bursts from bottom corners
      const interval = setInterval(() => {
        // Left corner
        confetti({
          particleCount: 25,
          angle: 45,
          spread: 40,
          origin: { x: 0.05, y: 0.95 },
          colors,
          startVelocity: 35,
          gravity: 0.9,
          scalar: 0.6,
        });

        // Right corner
        setTimeout(() => {
          confetti({
            particleCount: 25,
            angle: 135,
            spread: 40,
            origin: { x: 0.95, y: 0.95 },
            colors,
            startVelocity: 35,
            gravity: 0.9,
            scalar: 0.6,
          });
        }, 100);
      }, 600);

      // Clear interval after 2.5 seconds
      setTimeout(() => {
        clearInterval(interval);
      }, 2500);

      return () => {
        clearInterval(interval);
      };
    }
    return undefined;
  }, [isOpen]);

  const handleExploreExtras = () => {
    onClose();
    router.push('/extras');
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.4 }}
          className="fixed inset-0 z-[100] flex items-center justify-center p-4"
          onClick={onClose}
        >
          {/* Backdrop */}
          <div className="absolute inset-0 bg-black/80 backdrop-blur-sm" />

          {/* Modal Content - Redesigned to match your site */}
          <motion.div
            initial={{ scale: 0.9, opacity: 0, y: 30 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 30 }}
            transition={{
              type: 'spring',
              stiffness: 400,
              damping: 25,
              delay: 0.1,
            }}
            className="relative max-w-lg w-full bg-[hsl(var(--background))] border border-[hsl(var(--border))] rounded-xl shadow-2xl overflow-hidden"
            onClick={e => e.stopPropagation()}
          >
            {/* Header with close button */}
            <div className="flex items-center justify-between p-6 border-b border-[hsl(var(--border))]">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-[#a864fd] to-[#29cdff] rounded-lg flex items-center justify-center">
                  <Star className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-[hsl(var(--foreground))] flex items-center gap-2">
                    <PartyPopper className="h-5 w-5 text-[#a864fd]" />
                    Easter Egg Unlocked!
                  </h2>
                  <p className="text-sm text-[hsl(var(--muted-foreground))]">
                    Hidden feature discovered
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 rounded-lg hover:bg-[hsl(var(--accent))] transition-colors"
                aria-label="Close modal"
              >
                <X className="h-4 w-4 text-[hsl(var(--muted-foreground))]" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-center mb-6"
              >
                <div className="mb-4 flex justify-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-[#a864fd] to-[#29cdff] rounded-full flex items-center justify-center">
                    <Sparkles className="h-8 w-8 text-white" />
                  </div>
                </div>
                <h3 className="text-xl font-medium text-[hsl(var(--foreground))] mb-2">
                  Congratulations!
                </h3>
                <p className="text-[hsl(var(--muted-foreground))] leading-relaxed">
                  You've discovered the hidden extras section. Explore exclusive content,
                  experiments, and fun features that showcase additional work and projects.
                </p>
              </motion.div>

              {/* Action buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="flex flex-col sm:flex-row gap-3"
              >
                <button
                  onClick={handleExploreExtras}
                  className="flex-1 bg-[hsl(var(--primary))] hover:bg-[hsl(var(--primary))]/90 text-[hsl(var(--primary-foreground))] font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 group"
                >
                  <span>Explore Extras</span>
                  <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </button>

                <button
                  onClick={onClose}
                  className="flex-1 bg-[hsl(var(--secondary))] hover:bg-[hsl(var(--secondary))]/80 text-[hsl(var(--secondary-foreground))] font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  Maybe Later
                </button>
              </motion.div>

              {/* Keyboard shortcut hint */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="mt-6 p-4 bg-[hsl(var(--muted))] rounded-lg"
              >
                <p className="text-sm text-[hsl(var(--muted-foreground))] text-center flex items-center justify-center gap-2">
                  <Lightbulb className="h-4 w-4 text-[#fdff6a]" />
                  <span>
                    <strong>Pro tip:</strong> Use{' '}
                    <kbd className="px-2 py-1 bg-[hsl(var(--background))] border border-[hsl(var(--border))] rounded text-xs font-mono">
                      Ctrl+E
                    </kbd>{' '}
                    to quickly access extras anytime
                  </span>
                </p>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
