'use client';

import { ThemeName, useTheme } from '@/contexts/ThemeContext';
import { useCommandMenu } from '@/hooks/useCommandMenu';
import { Command } from 'cmdk';
import { AnimatePresence, motion } from 'framer-motion';
import {
  ArrowRight,
  Briefcase,
  Download,
  FileText,
  Home,
  Layers,
  Palette,
  Rss,
  Search,
  X,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

export default function CommandMenu() {
  const { isOpen, setIsOpen } = useCommandMenu();
  const { theme, setTheme } = useTheme();
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const [inputValue, setInputValue] = useState('');
  const [activeTab, setActiveTab] = useState<'all' | 'navigation' | 'themes'>('all');

  // Focus the input when the menu is opened and handle keyboard navigation
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 50);
    } else {
      setInputValue('');
      setActiveTab('all');
    }
  }, [isOpen]);

  // Handle keyboard navigation for tabs
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      // Tab navigation with Tab key
      if (e.key === 'Tab' && !e.shiftKey && !e.ctrlKey && !e.metaKey) {
        // Only handle tab navigation if we're not in the search input
        if (document.activeElement === inputRef.current) {
          e.preventDefault();
          const tabs: Array<'all' | 'navigation' | 'themes'> = ['all', 'navigation', 'themes'];
          const currentIndex = tabs.indexOf(activeTab);
          const nextIndex = (currentIndex + 1) % tabs.length;
          const nextTab = tabs[nextIndex];
          if (nextTab) setActiveTab(nextTab);
          return;
        }
      }

      // Shift+Tab for reverse navigation
      if (e.key === 'Tab' && e.shiftKey && !e.ctrlKey && !e.metaKey) {
        if (document.activeElement === inputRef.current) {
          e.preventDefault();
          const tabs: Array<'all' | 'navigation' | 'themes'> = ['all', 'navigation', 'themes'];
          const currentIndex = tabs.indexOf(activeTab);
          const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
          const prevTab = tabs[prevIndex];
          if (prevTab) setActiveTab(prevTab);
          return;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, activeTab]);

  // Navigation items with improved icons
  const navigationItems = [
    {
      icon: <Home className="h-5 w-5" />,
      label: 'Home',
      href: '/',
      description: 'Discover featured content',
    },
    {
      icon: <Briefcase className="h-5 w-5" />,
      label: 'Experience',
      href: '/experience',
      description: 'Professional background',
    },
    {
      icon: <Layers className="h-5 w-5" />,
      label: 'Projects',
      href: '/projects',
      description: 'Portfolio showcase',
    },
    // { icon: <Compass className="h-5 w-5" />, label: 'Services', href: '/services', description: 'Professional offerings' }, // Temporarily commented out
    {
      icon: <Download className="h-5 w-5" />,
      label: 'Resume',
      href: '/resume',
      description: 'Professional resume and CV',
    },
    {
      icon: <Rss className="h-5 w-5" />,
      label: 'Updates',
      href: '/feed',
      description: 'Latest updates',
    },
    {
      icon: <FileText className="h-5 w-5" />,
      label: 'Blog',
      href: '/blog',
      description: 'Articles and insights',
    },
  ];

  // Theme options with display names and descriptions
  const themeOptions: { value: ThemeName; label: string; description: string; color: string }[] = [
    {
      value: 'dark',
      label: 'Dark',
      description: 'Refined dark theme with subtle blue tint',
      color: '#1a1a2e',
    },
    {
      value: 'oceanic',
      label: 'Oceanic',
      description: 'Soothing blue theme inspired by the ocean depths',
      color: '#1e3a5f',
    },
    {
      value: 'midnight',
      label: 'Midnight',
      description: 'Deep blue midnight theme',
      color: '#0f172a',
    },
    {
      value: 'synthwave',
      label: 'Synthwave',
      description: 'Retro 80s neon vibes with softer contrast',
      color: '#2d1b69',
    },
    {
      value: 'terminal',
      label: 'Terminal',
      description: 'Soft green terminal theme',
      color: '#0c2b12',
    },
    {
      value: 'matrix',
      label: 'Matrix',
      description: 'Inspired by the Matrix movie',
      color: '#003b00',
    },
    {
      value: 'cyberpunk',
      label: 'Cyberpunk',
      description: 'High-tech, low-life aesthetic',
      color: '#2d0a4e',
    },
  ];

  const handleTabChange = (tab: 'all' | 'navigation' | 'themes') => {
    setActiveTab(tab);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm flex items-start justify-center pt-[10vh] p-4"
          onClick={() => setIsOpen(false)}
        >
          <motion.div
            initial={{ scale: 0.96, opacity: 0, y: -20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.96, opacity: 0, y: -20 }}
            transition={{ duration: 0.2, type: 'spring', stiffness: 400, damping: 30 }}
            className="w-full max-w-2xl rounded-xl border border-[hsl(var(--border))] bg-[hsl(var(--card))] shadow-2xl overflow-hidden"
            onClick={e => e.stopPropagation()}
          >
            <Command className="rounded-xl" shouldFilter={activeTab === 'all'}>
              {/* Enhanced Header */}
              <div className="flex items-center gap-3 px-6 py-5 border-b border-[hsl(var(--border))]">
                <div className="flex items-center justify-center w-9 h-9 rounded-lg bg-gradient-to-br from-[hsl(var(--primary))] to-[hsl(var(--primary))]/80 text-[hsl(var(--primary-foreground))] shadow-sm">
                  <Search className="h-4 w-4" />
                </div>
                <Command.Input
                  ref={inputRef}
                  value={inputValue}
                  onValueChange={setInputValue}
                  placeholder="Search commands, pages, or themes..."
                  className="flex h-10 w-full bg-transparent text-base outline-none placeholder:text-[hsl(var(--muted-foreground))] font-medium"
                />
                <div className="flex items-center gap-2">
                  <kbd className="hidden sm:inline-flex h-6 select-none items-center gap-1 rounded border border-[hsl(var(--border))] bg-[hsl(var(--muted))] px-2 font-mono text-[10px] font-medium text-[hsl(var(--muted-foreground))]">
                    ESC
                  </kbd>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="flex items-center justify-center w-8 h-8 rounded-lg text-[hsl(var(--muted-foreground))] hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))] transition-all duration-200"
                    aria-label="Close command menu"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Enhanced Tab Navigation */}
              <div className="border-b border-[hsl(var(--border))] bg-[hsl(var(--muted))]/20">
                <nav className="flex px-4 py-1" role="tablist">
                  <button
                    onClick={() => handleTabChange('all')}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleTabChange('all');
                      }
                    }}
                    role="tab"
                    aria-selected={activeTab === 'all'}
                    tabIndex={activeTab === 'all' ? 0 : -1}
                    className={`px-4 py-2.5 text-sm font-medium transition-all duration-200 relative rounded-lg mx-1 focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 ${
                      activeTab === 'all'
                        ? 'text-[hsl(var(--foreground))] bg-[hsl(var(--background))] shadow-sm'
                        : 'text-[hsl(var(--muted-foreground))] hover:text-[hsl(var(--foreground))] hover:bg-[hsl(var(--accent))]/50'
                    }`}
                  >
                    All
                  </button>
                  <button
                    onClick={() => handleTabChange('navigation')}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleTabChange('navigation');
                      }
                    }}
                    role="tab"
                    aria-selected={activeTab === 'navigation'}
                    tabIndex={activeTab === 'navigation' ? 0 : -1}
                    className={`px-4 py-2.5 text-sm font-medium transition-all duration-200 relative rounded-lg mx-1 focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 ${
                      activeTab === 'navigation'
                        ? 'text-[hsl(var(--foreground))] bg-[hsl(var(--background))] shadow-sm'
                        : 'text-[hsl(var(--muted-foreground))] hover:text-[hsl(var(--foreground))] hover:bg-[hsl(var(--accent))]/50'
                    }`}
                  >
                    Navigation
                  </button>
                  <button
                    onClick={() => handleTabChange('themes')}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleTabChange('themes');
                      }
                    }}
                    role="tab"
                    aria-selected={activeTab === 'themes'}
                    tabIndex={activeTab === 'themes' ? 0 : -1}
                    className={`px-4 py-2.5 text-sm font-medium transition-all duration-200 relative rounded-lg mx-1 focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 ${
                      activeTab === 'themes'
                        ? 'text-[hsl(var(--foreground))] bg-[hsl(var(--background))] shadow-sm'
                        : 'text-[hsl(var(--muted-foreground))] hover:text-[hsl(var(--foreground))] hover:bg-[hsl(var(--accent))]/50'
                    }`}
                  >
                    Themes
                  </button>
                </nav>
              </div>

              <Command.List className="max-h-[65vh] overflow-y-auto py-6 px-6 hide-scrollbar">
                {(activeTab === 'all' || activeTab === 'navigation') && (
                  <Command.Group heading="Navigation" className="pb-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {navigationItems.map(item => (
                        <Command.Item
                          key={item.label}
                          onSelect={() => {
                            router.push(item.href);
                            setIsOpen(false);
                          }}
                          className="flex items-center gap-4 px-4 py-4 rounded-xl text-sm cursor-pointer hover:bg-[hsl(var(--accent))] aria-selected:bg-[hsl(var(--accent))] transition-all duration-200 group border border-transparent hover:border-[hsl(var(--border))] hover:shadow-sm"
                          value={item.label}
                        >
                          <div className="flex items-center justify-center w-11 h-11 rounded-xl bg-gradient-to-br from-[hsl(var(--primary))]/10 to-[hsl(var(--primary))]/5 text-[hsl(var(--primary))] group-hover:from-[hsl(var(--primary))]/20 group-hover:to-[hsl(var(--primary))]/10 transition-all duration-200 group-hover:scale-105">
                            {item.icon}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="font-semibold text-[hsl(var(--foreground))] group-hover:text-[hsl(var(--primary))] transition-colors">
                              {item.label}
                            </div>
                            <div className="text-xs text-[hsl(var(--muted-foreground))] mt-0.5 truncate">
                              {item.description}
                            </div>
                          </div>
                          <ArrowRight className="h-4 w-4 text-[hsl(var(--muted-foreground))] opacity-0 group-hover:opacity-100 transition-all duration-200 group-hover:translate-x-1" />
                        </Command.Item>
                      ))}
                    </div>
                  </Command.Group>
                )}

                {(activeTab === 'all' || activeTab === 'themes') && (
                  <Command.Group heading="Themes" className="pt-4">
                    <div className="grid grid-cols-1 gap-3">
                      {themeOptions.map(option => (
                        <Command.Item
                          key={option.value}
                          onSelect={() => {
                            setTheme(option.value);
                          }}
                          className="flex items-center gap-4 px-4 py-4 rounded-xl text-sm cursor-pointer hover:bg-[hsl(var(--accent))] aria-selected:bg-[hsl(var(--accent))] transition-all duration-200 group border border-transparent hover:border-[hsl(var(--border))] hover:shadow-sm"
                          value={option.label}
                        >
                          <div
                            className={`flex items-center justify-center w-11 h-11 rounded-xl transition-all duration-300 shadow-sm ${
                              theme === option.value
                                ? 'ring-2 ring-[hsl(var(--primary))] ring-offset-2 ring-offset-[hsl(var(--background))] scale-105'
                                : 'group-hover:ring-1 group-hover:ring-[hsl(var(--border))] group-hover:scale-105'
                            }`}
                            style={{ backgroundColor: option.color }}
                          >
                            <Palette className="h-5 w-5 text-white drop-shadow-sm" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="font-semibold text-[hsl(var(--foreground))] group-hover:text-[hsl(var(--primary))] transition-colors">
                              {option.label}
                            </div>
                            <div className="text-xs text-[hsl(var(--muted-foreground))] mt-0.5 truncate">
                              {option.description}
                            </div>
                          </div>
                          {theme === option.value ? (
                            <span className="flex items-center justify-center px-3 py-1.5 rounded-lg text-xs font-semibold bg-gradient-to-r from-[hsl(var(--primary))] to-[hsl(var(--primary))]/80 text-[hsl(var(--primary-foreground))] shadow-sm">
                              Active
                            </span>
                          ) : (
                            <span className="text-xs text-[hsl(var(--muted-foreground))] opacity-0 group-hover:opacity-100 transition-all duration-200 font-medium">
                              Select
                            </span>
                          )}
                        </Command.Item>
                      ))}
                    </div>
                  </Command.Group>
                )}

                {inputValue &&
                  !navigationItems.some(item =>
                    item.label.toLowerCase().includes(inputValue.toLowerCase())
                  ) &&
                  !themeOptions.some(option =>
                    option.label.toLowerCase().includes(inputValue.toLowerCase())
                  ) && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="py-12 text-center"
                    >
                      <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-[hsl(var(--muted))] flex items-center justify-center">
                        <Search className="h-6 w-6 text-[hsl(var(--muted-foreground))]" />
                      </div>
                      <div className="text-sm font-medium text-[hsl(var(--foreground))] mb-1">
                        No results found
                      </div>
                      <div className="text-xs text-[hsl(var(--muted-foreground))]">
                        Try a different search term or browse categories
                      </div>
                    </motion.div>
                  )}
              </Command.List>

              {/* Enhanced Footer */}
              <div className="border-t border-[hsl(var(--border))] bg-[hsl(var(--muted))]/20 px-6 py-4">
                <div className="flex items-center justify-between text-xs text-[hsl(var(--muted-foreground))]">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1.5">
                      <kbd className="px-2 py-1 rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--background))] font-mono text-[10px] shadow-sm">
                        ↑↓
                      </kbd>
                      <span className="font-medium">Navigate</span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      <kbd className="px-2 py-1 rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--background))] font-mono text-[10px] shadow-sm">
                        ↵
                      </kbd>
                      <span className="font-medium">Select</span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      <kbd className="px-2 py-1 rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--background))] font-mono text-[10px] shadow-sm">
                        Tab
                      </kbd>
                      <span className="font-medium">Switch</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <kbd className="px-2 py-1 rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--background))] font-mono text-[10px] shadow-sm">
                      ESC
                    </kbd>
                    <span className="font-medium">Close</span>
                  </div>
                </div>
              </div>
            </Command>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
