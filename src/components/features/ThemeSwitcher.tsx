"use client";

import { useState, useEffect } from "react";
import { Palette, Check, ChevronDown } from "lucide-react";
import { useTheme, ThemeName } from "@/contexts/ThemeContext";
import { cn } from "@/lib/utils";

// Theme options with display names and descriptions
const themeOptions: { value: ThemeName; label: string; description: string }[] = [
  {
    value: "dark",
    label: "Dark",
    description: "Refined dark theme with subtle blue tint",
  },
  {
    value: "oceanic",
    label: "Oceanic",
    description: "Soothing blue theme inspired by the ocean depths",
  },
  {
    value: "midnight",
    label: "Midnight",
    description: "Deep blue midnight theme",
  },
  {
    value: "synthwave",
    label: "Synthwave",
    description: "Retro 80s neon vibes with softer contrast",
  },
  {
    value: "terminal",
    label: "Terminal",
    description: "Soft green terminal theme",
  },
  {
    value: "matrix",
    label: "Matrix",
    description: "Inspired by the Matrix movie",
  },
  {
    value: "cyberpunk",
    label: "Cyberpunk",
    description: "High-tech, low-life aesthetic",
  },

];

export default function ThemeSwitcher() {
  const { theme, setTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Set mounted to true on client side
  useEffect(() => {
    setMounted(true);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    if (!isOpen) return;

    function handleClickOutside() {
      setIsOpen(false);
    }

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, [isOpen]);

  // Get current theme option
  const currentTheme = themeOptions.find((t) => t.value === theme) || themeOptions[0];

  // If not mounted yet, don't render anything to avoid hydration mismatch
  if (!mounted) return null;

  return (
    <div className="relative">
      <button
        id="theme-menu-button"
        onClick={(e) => {
          e.stopPropagation();
          setIsOpen(!isOpen);
        }}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setIsOpen(!isOpen);
          } else if (e.key === 'Escape' && isOpen) {
            e.preventDefault();
            setIsOpen(false);
          }
        }}
        aria-haspopup="true"
        aria-expanded={isOpen}
        aria-label={`Theme switcher, current theme: ${currentTheme.label}`}
        className="flex w-full items-center justify-between gap-2 rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--secondary))] px-3 py-2 text-sm text-[hsl(var(--secondary-foreground))] hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))] transition-colors focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 focus:ring-offset-[hsl(var(--background))]"
      >
        <div className="flex items-center gap-2">
          <Palette className="h-4 w-4" />
          <span>{currentTheme.label}</span>
        </div>
        <ChevronDown className={cn("h-4 w-4 transition-transform", isOpen && "rotate-180")} />
      </button>

      {isOpen && (
        <div
          className="absolute left-0 right-0 top-full z-50 mt-1 rounded-md border border-[hsl(var(--border))] bg-[hsl(var(--secondary))] p-1 shadow-lg"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="theme-menu-button"
        >
          <div className="py-1 space-y-0.5">
            {themeOptions.map((option) => (
              <button
                key={option.value}
                onClick={(e) => {
                  e.stopPropagation();
                  setTheme(option.value);
                  setIsOpen(false);
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    setTheme(option.value);
                    setIsOpen(false);
                  } else if (e.key === 'Escape') {
                    e.preventDefault();
                    setIsOpen(false);
                  }
                }}
                role="menuitem"
                tabIndex={0}
                aria-label={`${option.label} theme: ${option.description}`}
                className={cn(
                  "flex w-full items-center justify-between rounded-md px-2 py-1.5 text-sm text-[hsl(var(--secondary-foreground))] hover:bg-[hsl(var(--accent))] hover:text-[hsl(var(--accent-foreground))] transition-colors focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))]",
                  theme === option.value && "bg-[hsl(var(--accent))] text-[hsl(var(--accent-foreground))]"
                )}
              >
                <div className="flex flex-col items-start">
                  <span>{option.label}</span>
                  <span className="text-xs text-[hsl(var(--muted-foreground))]">{option.description}</span>
                </div>
                {theme === option.value && <Check className="h-4 w-4 text-[hsl(var(--primary))]" />}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
