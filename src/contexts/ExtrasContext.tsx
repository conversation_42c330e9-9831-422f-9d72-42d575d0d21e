"use client";

import React, { createContext, useContext, useState, ReactNode, useCallback } from 'react';

interface ExtrasContextType {
  isExtrasUnlocked: boolean;
  setIsExtrasUnlocked: (unlocked: boolean) => void;
  showUnlockModal: boolean;
  setShowUnlockModal: (show: boolean) => void;
  triggerUnlock: () => void;
}

const ExtrasContext = createContext<ExtrasContextType | undefined>(undefined);

export function ExtrasProvider({ children }: { children: ReactNode }) {
  const [isExtrasUnlocked, setIsExtrasUnlocked] = useState(false);
  const [showUnlockModal, setShowUnlockModal] = useState(false);

  const triggerUnlock = useCallback(() => {
    // Use setTimeout to avoid setState during render
    setTimeout(() => {
      setIsExtrasUnlocked(true);
      setShowUnlockModal(true);
    }, 0);
  }, []);

  return (
    <ExtrasContext.Provider value={{
      isExtrasUnlocked,
      setIsExtrasUnlocked,
      showUnlockModal,
      setShowUnlockModal,
      triggerUnlock
    }}>
      {children}
    </ExtrasContext.Provider>
  );
}

export function useExtras() {
  const context = useContext(ExtrasContext);
  if (context === undefined) {
    throw new Error('useExtras must be used within an ExtrasProvider');
  }
  return context;
}
