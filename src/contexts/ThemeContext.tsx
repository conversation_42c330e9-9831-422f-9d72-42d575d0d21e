"use client";

import React, { createContext, useContext, useEffect, useState } from "react";

// Define theme types
export type ThemeName =
  | "dark"
  | "oceanic"
  | "midnight"
  | "synthwave"
  | "cyberpunk"
  | "terminal"
  | "matrix";

// Theme context type
type ThemeContextType = {
  theme: ThemeName;
  setTheme: (theme: ThemeName) => void;
};

// Create context with default values
const ThemeContext = createContext<ThemeContextType>({
  theme: "dark",
  setTheme: () => null,
});

// Hook to use theme context
export const useTheme = () => useContext(ThemeContext);

// Theme provider component
export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<ThemeName>("dark");
  const [mounted, setMounted] = useState(false);

  // Update theme class on document element
  useEffect(() => {
    if (!mounted) return;

    const applyTheme = (themeName: ThemeName) => {
      // Remove all theme classes
      document.documentElement.classList.remove(
        "theme-dark",
        "theme-oceanic",
        "theme-midnight",
        "theme-synthwave",
        "theme-cyberpunk",
        "theme-terminal",
        "theme-matrix"
      );

      // Add current theme class
      document.documentElement.classList.add(`theme-${themeName}`);

      // Save theme preference to localStorage
      localStorage.setItem("theme", themeName);
    };

    // Apply theme immediately
    applyTheme(theme);
  }, [theme, mounted]);

  // Initialize theme from localStorage on mount
  useEffect(() => {
    setMounted(true);
    const savedTheme = localStorage.getItem("theme") as ThemeName;
    if (savedTheme) {
      setTheme(savedTheme);
    }
  }, []);

  // Prevent flash of default theme during SSR
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}
