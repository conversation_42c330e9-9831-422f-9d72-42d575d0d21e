export interface Project {
  title: string
  slug: string
  shortDescription: string
  date: string
  tags: string[]
  imageUrl: string
  demoUrl?: string
  sourceUrl?: string
  content: string
}

export interface Experience {
  company: string
  title: string
  startDate: string
  endDate: string | null
  description: string
  technologies: string[]
}

export interface BlogPost {
  title: string
  slug: string
  date: string
  excerpt: string
  tags: string[]
  content: string
  source?: 'local' | 'medium' | 'external'
  externalUrl?: string
  readTime?: string
  author?: string
  thumbnail?: string
  mediumClaps?: number
}

// Medium-specific blog post interface
export interface MediumArticle {
  title: string
  link: string
  pubDate: string
  description: string
  content: string
  guid: string
  categories: string[]
  thumbnail?: string
  author: string
  readTime?: string
}

// Enhanced blog post for unified display
export interface EnhancedBlogPost extends BlogPost {
  isNew?: boolean
  daysOld?: number
}

// Keep the old interface for backward compatibility
export type Thought = BlogPost;

export interface FeedItem {
  date: string
  content: string
}

export interface Tool {
  name: string
  description: string
  category: string
  tags: string[]
  icon: string
}
