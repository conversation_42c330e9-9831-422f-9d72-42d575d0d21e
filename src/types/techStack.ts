/**
 * Shared types for tech stack data used across the application
 */

// Tool represents a single technology or tool
export interface Tool {
  name: string;
  description: string;
  tags: string[];
  website: string;
  level: "Expert" | "Advanced" | "Intermediate";
  status: "Core Skill" | "Learning";
}

// TechCategory represents a category containing multiple tools
export interface TechCategory {
  category: string;
  icon: string | any; // Support both string and LucideIcon components
  color: string;
  description: string;
  tools: Tool[];
}

// Raw record from Airtable
export interface TechStackRecord {
  id: string;
  name: string;
  description: string;
  website: string;
  level: string;
  status: string;
  category: string;
  tags: string;
  categoryIcon: string;
  categoryColor: string;
  categoryDescription: string;
  displayOrder: number;
  categoryOrder: number;
}
