# Backend Architecture & API Documentation

## API Structure

### Route Organization
```
src/app/api/
├── blog/
│   ├── route.ts          # GET /api/blog - All blog posts
│   └── medium/
│       └── route.ts      # GET /api/blog/medium - Medium posts
├── contact/
│   └── route.ts          # POST /api/contact - Contact form
└── resume/
    └── download/
        └── route.ts      # GET /api/resume/download - PDF download
```

### API Response Standards

#### Success Response Format
```typescript
interface ApiResponse<T> {
  success: true;
  data: T;
  message?: string;
  timestamp: string;
  meta?: {
    count?: number;
    page?: number;
    totalPages?: number;
    lastUpdated?: string;
  };
}
```

#### Error Response Format
```typescript
interface ApiErrorResponse {
  success: false;
  error: string;
  code: ApiErrorCode;
  timestamp: string;
  details?: unknown;
}
```

## API Endpoints

### Blog API

#### GET /api/blog
Fetches all blog posts from configured sources.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "title": "Post Title",
      "slug": "post-slug",
      "excerpt": "Post excerpt...",
      "pubDate": "2024-01-15T10:00:00Z",
      "link": "https://medium.com/@user/post",
      "categories": ["tech", "web-dev"],
      "readTime": "5 min read"
    }
  ],
  "meta": {
    "count": 10,
    "lastUpdated": "2024-01-15T10:00:00Z"
  },
  "timestamp": "2024-01-15T10:00:00Z"
}
```

#### GET /api/blog/medium
Fetches blog posts specifically from Medium RSS feed.

**Features:**
- RSS feed parsing
- Content sanitization
- Caching with TTL
- Error fallback

### Contact API

#### POST /api/contact
Handles contact form submissions with validation and rate limiting.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "message": "Hello, I'd like to connect...",
  "subject": "Collaboration Inquiry",
  "company": "Tech Corp"
}
```

**Validation Rules:**
- Name: 2-100 characters
- Email: Valid email format
- Message: 10-2000 characters
- Subject: Optional, 1-200 characters
- Company: Optional, 1-100 characters

**Rate Limiting:**
- 5 requests per minute per IP
- Sliding window implementation

### Resume API

#### GET /api/resume/download
Serves PDF resume with proper download headers.

**Features:**
- File existence validation
- Proper MIME types
- Download headers
- Caching headers
- Rate limiting

## Middleware & Utilities

### Request Context
```typescript
interface RequestContext {
  ip: string;
  userAgent: string;
  timestamp: number;
  requestId: string;
}
```

### Rate Limiting
```typescript
// Configuration
const RATE_LIMIT_CONFIGS = {
  strict: { windowMs: 60 * 1000, maxRequests: 5 },
  moderate: { windowMs: 60 * 1000, maxRequests: 30 },
  lenient: { windowMs: 60 * 1000, maxRequests: 100 },
};

// Usage
const { isLimited } = checkRateLimit(request, RATE_LIMIT_CONFIGS.strict);
```

### Input Validation
```typescript
// Contact form validation
export function validateContactForm(data: ContactFormData): ValidationResult {
  const errors: string[] = [];
  
  // Validate required fields
  if (!data.name || data.name.length < 2) {
    errors.push('Name must be at least 2 characters long');
  }
  
  // Validate email format
  if (!validateEmail(data.email)) {
    errors.push('Please provide a valid email address');
  }
  
  return { isValid: errors.length === 0, errors };
}
```

### Input Sanitization
```typescript
// Sanitize user input
export function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}
```

## Error Handling

### Error Types
```typescript
enum ApiErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  RATE_LIMITED = 'RATE_LIMITED',
  NOT_FOUND = 'NOT_FOUND',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
}
```

### Error Response Helpers
```typescript
// Standardized error responses
export function createErrorResponse(error: ApiError | string) {
  return NextResponse.json({
    success: false,
    error: typeof error === 'string' ? error : error.message,
    code: typeof error === 'string' ? 'INTERNAL_ERROR' : error.code,
    timestamp: new Date().toISOString(),
  }, { 
    status: typeof error === 'string' ? 500 : error.statusCode 
  });
}
```

## Security Implementation

### CORS Configuration
```typescript
// CORS headers for API responses
export function createCorsHeaders(methods: string[] = ['GET', 'OPTIONS']) {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': methods.join(', '),
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400',
  };
}
```

### Security Headers
```typescript
// Security headers in next.config.js
headers: [
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff',
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY',
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block',
  },
]
```

## Data Integration

### Medium RSS Integration
```typescript
// Fetch and parse Medium RSS feed
export async function fetchMediumBlogPosts(): Promise<BlogPost[]> {
  try {
    const response = await fetch(MEDIUM_RSS_URL, {
      next: { revalidate: 3600 }, // Cache for 1 hour
    });
    
    const xmlText = await response.text();
    const posts = await parseRSSFeed(xmlText);
    
    return posts.map(transformMediumPost);
  } catch (error) {
    console.error('Failed to fetch Medium posts:', error);
    return [];
  }
}
```

### Caching Strategy
- **Static Generation** for content pages
- **ISR (Incremental Static Regeneration)** for blog content
- **API Response Caching** with appropriate TTL
- **Browser Caching** with proper headers

## Monitoring & Logging

### Request Logging
```typescript
export function logRequest(
  request: NextRequest,
  context: RequestContext,
  additionalInfo?: Record<string, unknown>
): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[API] ${request.method} ${request.url}`, {
      requestId: context.requestId,
      ip: context.ip,
      timestamp: new Date(context.timestamp).toISOString(),
      ...additionalInfo,
    });
  }
}
```

### Error Tracking
- **Development**: Detailed error logs with stack traces
- **Production**: Sanitized error messages for users
- **Monitoring**: Error aggregation and alerting

## Performance Optimizations

### Response Optimization
- **Compression** enabled in Next.js config
- **Efficient JSON serialization**
- **Minimal response payloads**
- **Proper HTTP status codes**

### Database Considerations
- **Connection pooling** for database connections
- **Query optimization** with proper indexing
- **Caching layers** for frequently accessed data
- **Pagination** for large datasets

## Development Workflow

### API Testing
```typescript
// Example API test
describe('/api/contact', () => {
  it('should validate required fields', async () => {
    const response = await fetch('/api/contact', {
      method: 'POST',
      body: JSON.stringify({}),
    });
    
    expect(response.status).toBe(400);
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.errors).toContain('Name is required');
  });
});
```

### Environment Configuration
```typescript
// Environment variables
MEDIUM_RSS_URL=https://medium.com/feed/@username
EMAILJS_SERVICE_ID=service_id
EMAILJS_TEMPLATE_ID=template_id
EMAILJS_PUBLIC_KEY=public_key
```
