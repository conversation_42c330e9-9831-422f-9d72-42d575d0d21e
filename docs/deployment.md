# Deployment Guide

## Build Process

### Production Build
```bash
# Install dependencies
npm install

# Type checking
npm run type-check

# Linting
npm run lint

# Build for production
npm run build

# Start production server
npm start
```

### Build Optimization
- **Static Generation**: Pages are pre-rendered at build time
- **Bundle Optimization**: Tree shaking and code splitting
- **Asset Optimization**: Images, fonts, and static assets
- **Performance Analysis**: Bundle size monitoring

## Environment Configuration

### Required Environment Variables
```bash
# Blog Integration
MEDIUM_RSS_URL=https://medium.com/feed/@yourusername

# Contact Form (EmailJS)
EMAILJS_SERVICE_ID=your_service_id
EMAILJS_TEMPLATE_ID=your_template_id
EMAILJS_PUBLIC_KEY=your_public_key

# Voice Assistant (Vapi AI)
VAPI_ASSISTANT_ID=your_assistant_id
VAPI_PUBLIC_KEY=your_public_key
```

### Optional Environment Variables
```bash
# Analytics
GOOGLE_ANALYTICS_ID=your_ga_id
VERCEL_ANALYTICS_ID=your_vercel_analytics_id

# Development
NODE_ENV=production
ANALYZE=false
```

## Deployment Platforms

### Vercel (Recommended)

#### Automatic Deployment
1. **Connect Repository**: Link your GitHub repository to Vercel
2. **Configure Environment**: Add environment variables in Vercel dashboard
3. **Deploy**: Automatic deployment on every push to main branch

#### Vercel Configuration
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "installCommand": "npm install",
  "framework": "nextjs"
}
```

#### Custom Domain Setup
1. Add domain in Vercel dashboard
2. Configure DNS records
3. SSL certificate automatically provisioned

### Netlify

#### Build Configuration
```toml
[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  portfolio:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MEDIUM_RSS_URL=${MEDIUM_RSS_URL}
      - EMAILJS_SERVICE_ID=${EMAILJS_SERVICE_ID}
      - EMAILJS_TEMPLATE_ID=${EMAILJS_TEMPLATE_ID}
      - EMAILJS_PUBLIC_KEY=${EMAILJS_PUBLIC_KEY}
    restart: unless-stopped
```

## Performance Optimization

### Build Optimization
```javascript
// next.config.js optimizations
const nextConfig = {
  // Image optimization
  images: {
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
  },
  
  // Compression
  compress: true,
  
  // Headers for caching
  async headers() {
    return [
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};
```

### CDN Configuration
- **Static Assets**: Serve from CDN for global distribution
- **Image Optimization**: Use Next.js Image optimization
- **Font Loading**: Optimize font loading with font-display: swap

## Monitoring & Analytics

### Performance Monitoring
```javascript
// Core Web Vitals tracking
export function reportWebVitals(metric) {
  if (metric.label === 'web-vital') {
    console.log(metric);
    // Send to analytics service
  }
}
```

### Error Tracking
```javascript
// Error boundary for production
class ErrorBoundary extends React.Component {
  componentDidCatch(error, errorInfo) {
    // Log error to monitoring service
    console.error('Error caught by boundary:', error, errorInfo);
  }
}
```

### Bundle Analysis
```bash
# Analyze bundle size
npm run analyze

# Check bundle size
npm run check-bundle
```

## Security Configuration

### Security Headers
```javascript
// next.config.js security headers
async headers() {
  return [
    {
      source: '/(.*)',
      headers: [
        {
          key: 'X-Content-Type-Options',
          value: 'nosniff',
        },
        {
          key: 'X-Frame-Options',
          value: 'DENY',
        },
        {
          key: 'X-XSS-Protection',
          value: '1; mode=block',
        },
        {
          key: 'Referrer-Policy',
          value: 'origin-when-cross-origin',
        },
      ],
    },
  ];
}
```

### Content Security Policy
```javascript
// CSP configuration
const ContentSecurityPolicy = `
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline' *.vercel-analytics.com;
  style-src 'self' 'unsafe-inline';
  img-src 'self' blob: data: https:;
  font-src 'self';
  object-src 'none';
  base-uri 'self';
  form-action 'self';
  frame-ancestors 'none';
  upgrade-insecure-requests;
`;
```

## Backup & Recovery

### Database Backup
- **Content Backup**: Regular backup of dynamic content
- **Configuration Backup**: Environment variables and settings
- **Asset Backup**: Static assets and media files

### Disaster Recovery
1. **Repository Backup**: Code stored in Git repository
2. **Environment Recreation**: Infrastructure as code
3. **Data Recovery**: Restore from backups
4. **DNS Failover**: Backup domain configuration

## Maintenance

### Regular Tasks
- **Dependency Updates**: Monthly security updates
- **Performance Monitoring**: Weekly performance reviews
- **Bundle Size Monitoring**: Track bundle size changes
- **Security Audits**: Quarterly security reviews

### Update Process
```bash
# Check for updates
npm outdated

# Update dependencies
npm update

# Security audit
npm audit

# Fix security issues
npm audit fix
```

## Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clear cache and rebuild
npm run clean
npm install
npm run build
```

#### Performance Issues
```bash
# Analyze bundle
npm run analyze

# Check bundle size
npm run check-bundle

# Profile performance
npm run dev -- --profile
```

#### Environment Issues
```bash
# Verify environment variables
echo $MEDIUM_RSS_URL
echo $EMAILJS_SERVICE_ID

# Test API endpoints
curl https://yourdomain.com/api/blog
```

### Debugging
```javascript
// Development debugging
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', debugData);
}

// Production error logging
if (process.env.NODE_ENV === 'production') {
  // Send to error tracking service
  errorTracker.captureException(error);
}
```
