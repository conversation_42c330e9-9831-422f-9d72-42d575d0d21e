# Setting Up Your Tech Stack in Airtable

This guide will walk you through setting up a single table in Airtable to store your tech stack data.

## 1. Create a Base

1. Create a new base in Airtable named "Portfolio Tech Stack"
2. Create a single table named "Tech Stack"

## 2. Set Up Fields

Add the following fields to your Tech Stack table:

| Field Name           | Field Type       | Description                                          |
| -------------------- | ---------------- | ---------------------------------------------------- |
| Name                 | Single line text | Name of the technology (e.g., "TypeScript")          |
| Description          | Long text        | Brief description of what the technology does        |
| Website              | URL              | Link to the official documentation or website        |
| Level                | Single select    | Proficiency level: Expert, Advanced, or Intermediate |
| Status               | Single select    | Usage status: Core Skill or Learning                 |
| Category             | Single line text | The category this tool belongs to                    |
| Tags                 | Long text        | Comma-separated list of tags                         |
| Category Icon        | Single line text | Icon name from Lucide (e.g., "Monitor", "Server")    |
| Category Color       | Single line text | Color for the category (e.g., "blue", "green")       |
| Category Description | Long text        | Brief description of the category                    |
| Display Order        | Number           | The order to display tools within a category         |
| Category Order       | Number           | The order to display categories                      |

## 3. Configure Single Select Fields

### Level Field:

- Expert
- Advanced
- Intermediate

### Status Field:

- Core Skill
- Learning

## 4. Example Data Entry

Here's an example of how to enter a technology:

| Field                | Example Value                                               |
| -------------------- | ----------------------------------------------------------- |
| Name                 | TypeScript                                                  |
| Description          | Strongly typed JavaScript for better development experience |
| Website              | https://typescriptlang.org                                  |
| Level                | Expert                                                      |
| Status               | Core Skill                                                  |
| Category             | Languages                                                   |
| Tags                 | Type Safety, Developer Experience, Scalable                 |
| Category Icon        | Monitor                                                     |
| Category Color       | blue                                                        |
| Category Description | Core programming languages I work with                      |
| Display Order        | 1                                                           |
| Category Order       | 1                                                           |

## 5. Efficiently Adding Multiple Technologies

When adding multiple technologies in the same category:

1. Add the first technology with all details
2. For subsequent technologies in the same category, copy the category-related fields:
   - Category
   - Category Icon
   - Category Color
   - Category Description
   - Category Order

This ensures consistency across your tech stack data.

## 6. Environment Variables

Make sure to set these environment variables in your project:

```
AIRTABLE_API_KEY=your_airtable_api_key
AIRTABLE_TECH_STACK_BASE_ID=your_airtable_base_id
```

You can find your Base ID in the Airtable API documentation for your base.
