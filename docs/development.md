# Development Guidelines

## Getting Started

### Prerequisites
- **Node.js**: Version 18 or higher
- **npm**: Version 8 or higher
- **Git**: For version control
- **VS Code**: Recommended editor with extensions

### Initial Setup
```bash
# Clone the repository
git clone <repository-url>
cd hyperfox-portfolio

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env.local

# Start development server
npm run dev
```

### Development Environment
```bash
# Available scripts
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run type-check   # TypeScript type checking
npm run format       # Format code with Prettier
npm run analyze      # Analyze bundle size
npm run clean        # Clean build artifacts
```

## Code Standards

### File Naming Conventions
- **Components**: PascalCase (e.g., `Button.tsx`, `BlogCard.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useBlogData.ts`)
- **Utilities**: camelCase (e.g., `utils.ts`, `apiHelpers.ts`)
- **Pages**: kebab-case (e.g., `blog-post.tsx`)
- **Types**: PascalCase (e.g., `BlogPost.ts`, `ApiResponse.ts`)

### Directory Structure
```
src/
├── app/                 # Next.js App Router pages
├── components/          # React components
│   ├── ui/             # Base UI components
│   ├── common/         # Shared components
│   ├── features/       # Feature-specific components
│   └── layout/         # Layout components
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries
├── types/              # TypeScript type definitions
├── config/             # Configuration files
└── content/            # Static content (MDX)
```

### Import Organization
```typescript
// 1. React and Next.js imports
import { useState, useEffect } from 'react';
import { NextRequest, NextResponse } from 'next/server';

// 2. Third-party library imports
import { motion, AnimatePresence } from 'framer-motion';
import { Mail, Github, Linkedin } from 'lucide-react';

// 3. Internal imports (absolute paths)
import { Button } from '@/components/ui';
import { useBlogData } from '@/hooks';
import { cn } from '@/lib/utils';

// 4. Relative imports
import './styles.css';
```

## Component Development

### Component Template
```typescript
/**
 * ComponentName
 * 
 * Brief description of what this component does.
 * 
 * @example
 * ```tsx
 * <ComponentName prop="value" />
 * ```
 */

"use client"; // Only if client-side features are needed

import { useState } from 'react';
import { cn } from '@/lib/utils';

interface ComponentNameProps {
  prop: string;
  optionalProp?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export default function ComponentName({ 
  prop, 
  optionalProp = false, 
  className,
  children 
}: ComponentNameProps) {
  const [state, setState] = useState(false);

  return (
    <div className={cn("base-classes", className)}>
      {children}
    </div>
  );
}

// Named export for easier testing
export { ComponentName };
```

### Component Guidelines
- **Single Responsibility**: Each component should have one clear purpose
- **Composition**: Prefer composition over inheritance
- **Props Interface**: Always define TypeScript interfaces for props
- **Default Props**: Use default parameters instead of defaultProps
- **Accessibility**: Include proper ARIA labels and semantic HTML

### Styling Guidelines
```typescript
// Use cn utility for conditional classes
className={cn(
  "base-classes",
  variant === "primary" && "primary-classes",
  isActive && "active-classes",
  className // Allow className override
)}

// Responsive design with Tailwind
className="text-sm md:text-base lg:text-lg"

// Theme-aware styling
className="bg-[hsl(var(--background))] text-[hsl(var(--foreground))]"
```

## Hook Development

### Custom Hook Template
```typescript
/**
 * useHookName
 * 
 * Description of what this hook does and when to use it.
 * 
 * @param param - Description of parameter
 * @returns Object with hook state and methods
 */

import { useState, useEffect, useCallback } from 'react';

interface UseHookNameOptions {
  option?: boolean;
}

interface UseHookNameReturn {
  data: any[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useHookName(
  param: string,
  options: UseHookNameOptions = {}
): UseHookNameReturn {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refetch = useCallback(() => {
    // Refetch logic
  }, [param]);

  useEffect(() => {
    // Effect logic
  }, [param, options]);

  return { data, loading, error, refetch };
}
```

## API Development

### API Route Template
```typescript
/**
 * API Route: /api/endpoint
 * 
 * Description of what this endpoint does.
 */

import { NextRequest } from 'next/server';
import { 
  createSuccessResponse, 
  createErrorResponse,
  getRequestContext,
  checkRateLimit,
  RATE_LIMIT_CONFIGS 
} from '@/lib/api';

export async function GET(request: NextRequest) {
  const context = getRequestContext(request);

  try {
    // Rate limiting
    const { isLimited } = checkRateLimit(request, RATE_LIMIT_CONFIGS.moderate);
    if (isLimited) {
      return createErrorResponse({
        code: 'RATE_LIMITED',
        message: 'Too many requests',
        statusCode: 429,
      });
    }

    // Business logic
    const data = await fetchData();

    return createSuccessResponse(data, {
      meta: { count: data.length }
    });
  } catch (error) {
    console.error('API error:', error);
    return createErrorResponse({
      code: 'INTERNAL_ERROR',
      message: 'Internal server error',
      statusCode: 500,
    });
  }
}
```

## Testing Guidelines

### Component Testing
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies variant classes correctly', () => {
    render(<Button variant="primary">Primary</Button>);
    expect(screen.getByRole('button')).toHaveClass('primary-classes');
  });
});
```

### Hook Testing
```typescript
import { renderHook, act } from '@testing-library/react';
import { useBlogData } from './useBlogData';

describe('useBlogData Hook', () => {
  it('should fetch blog data on mount', async () => {
    const { result } = renderHook(() => useBlogData());
    
    expect(result.current.loading).toBe(true);
    
    await act(async () => {
      // Wait for data to load
    });
    
    expect(result.current.loading).toBe(false);
    expect(result.current.posts).toHaveLength(5);
  });
});
```

## Performance Guidelines

### Optimization Techniques
```typescript
// Memoization for expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);

// Callback memoization
const handleClick = useCallback(() => {
  onClick(id);
}, [onClick, id]);

// Component memoization
const MemoizedComponent = memo(ExpensiveComponent);

// Dynamic imports for code splitting
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <Spinner />,
  ssr: false,
});
```

### Bundle Size Monitoring
```bash
# Check bundle size after changes
npm run build
npm run analyze

# Monitor bundle size
npm run check-bundle
```

## Git Workflow

### Branch Naming
- **Feature**: `feature/add-blog-pagination`
- **Bug Fix**: `fix/contact-form-validation`
- **Hotfix**: `hotfix/security-patch`
- **Refactor**: `refactor/component-structure`

### Commit Messages
```
type(scope): description

feat(blog): add pagination to blog posts
fix(contact): resolve form validation issues
docs(readme): update installation instructions
refactor(components): reorganize component structure
perf(images): optimize image loading
```

### Pull Request Process
1. **Create Feature Branch**: From main branch
2. **Implement Changes**: Follow coding standards
3. **Write Tests**: Ensure adequate test coverage
4. **Update Documentation**: Update relevant docs
5. **Create Pull Request**: With clear description
6. **Code Review**: Address feedback
7. **Merge**: Squash and merge to main

## Debugging

### Development Tools
```typescript
// React Developer Tools
// - Component tree inspection
// - Props and state debugging
// - Performance profiling

// Next.js debugging
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', debugData);
}

// Network debugging
// - Browser DevTools Network tab
// - API response inspection
// - Performance timing
```

### Common Issues
```typescript
// Hydration mismatches
// Solution: Ensure server and client render the same content

// Memory leaks
// Solution: Clean up subscriptions and timers
useEffect(() => {
  const timer = setInterval(() => {}, 1000);
  return () => clearInterval(timer);
}, []);

// Performance issues
// Solution: Use React DevTools Profiler
// Identify unnecessary re-renders
```

## Code Quality

### ESLint Configuration
```json
{
  "extends": ["next/core-web-vitals", "next/typescript"],
  "rules": {
    "react/no-unescaped-entities": "off",
    "@typescript-eslint/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }]
  }
}
```

### TypeScript Configuration
```json
{
  "compilerOptions": {
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

### Pre-commit Hooks
```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": ["eslint --fix", "prettier --write"],
    "*.{json,md}": ["prettier --write"]
  }
}
```
