# Frontend Architecture & Patterns

## Component Structure

### Component Hierarchy
The frontend follows a hierarchical component structure with clear separation of concerns:

```
Components/
├── UI Components (Atomic)
│   ├── Button - Reusable button with variants
│   ├── Card - Content containers
│   └── Input - Form inputs
├── Common Components (Molecular)
│   ├── PageLoader - Loading states
│   ├── ProjectCard - Project display
│   └── FlippablePhoto - Interactive images
├── Feature Components (Organisms)
│   ├── CommandMenu - Global command palette
│   ├── ThemeSwitcher - Theme management
│   └── VoiceAssistantBot - AI integration
└── Layout Components (Templates)
    ├── Sidebar - Navigation
    ├── Footer - Site footer
    └── RootLayoutClient - Main layout
```

### Component Patterns

#### 1. Composition Pattern
```tsx
// Example: Card composition
<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
    <CardDescription>Description</CardDescription>
  </CardHeader>
  <CardContent>
    Content goes here
  </CardContent>
</Card>
```

#### 2. Render Props Pattern
```tsx
// Example: Data fetching with render props
const { posts, loading, error } = useBlogData();
```

#### 3. Compound Components
```tsx
// Example: Pagination compound component
<Pagination currentPage={1} totalPages={10} onPageChange={handleChange} />
```

## State Management

### Context Providers
- **ThemeProvider** - Global theme state and switching
- **ExtrasProvider** - Feature toggles and extras state

### Custom Hooks
- **useBlogData** - Blog data fetching and caching
- **usePagination** - Pagination logic
- **useCommandMenu** - Command menu state
- **usePageLoader** - Loading state management
- **useFocusManagement** - Accessibility focus handling

### State Patterns
```tsx
// Local state for component-specific data
const [isOpen, setIsOpen] = useState(false);

// Context for global state
const { theme, setTheme } = useTheme();

// Custom hooks for complex logic
const { posts, loading, refreshPosts } = useBlogData();
```

## Styling Architecture

### Tailwind CSS Organization
- **Utility-first** approach for rapid development
- **Component classes** for reusable patterns
- **CSS variables** for theme customization
- **Responsive design** with mobile-first approach

### Theme System
```css
/* CSS Variables for theming */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --secondary: 210 40% 96%;
}
```

### Component Styling Patterns
```tsx
// Using cn utility for conditional classes
className={cn(
  "base-classes",
  variant === "primary" && "primary-classes",
  isActive && "active-classes",
  className
)}
```

## Performance Optimizations

### Code Splitting
```tsx
// Dynamic imports for heavy components
const CommandMenu = dynamic(() => import("@/components/features/CommandMenu"), {
  ssr: false,
  loading: () => null,
});
```

### Memoization
```tsx
// Memoized components for expensive renders
const MemoizedBlogCard = memo(AnimatedBlogCard);

// Memoized values for expensive calculations
const paginationInfo = useMemo(() => ({
  currentPage,
  totalPages,
  totalItems: posts.length,
}), [currentPage, totalPages, posts.length]);
```

### Image Optimization
```tsx
// Next.js Image component with optimization
<Image
  src="/image.jpg"
  alt="Description"
  width={400}
  height={300}
  priority={isAboveFold}
  placeholder="blur"
/>
```

## Animation & Interactions

### Framer Motion Integration
```tsx
// Page transitions
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  exit={{ opacity: 0, y: -20 }}
  transition={{ duration: 0.3 }}
>
  Content
</motion.div>
```

### Loading States
- **Skeleton loaders** with shimmer effects
- **Progressive loading** with staggered animations
- **Smooth transitions** between states

## Accessibility Implementation

### ARIA Patterns
```tsx
// Proper ARIA labeling
<button
  aria-label="Close dialog"
  aria-expanded={isOpen}
  aria-controls="dialog-content"
>
  <X className="h-4 w-4" />
</button>
```

### Keyboard Navigation
- **Tab order management** with TabFocusManager
- **Keyboard shortcuts** with useKeyboardShortcuts
- **Focus restoration** after modal interactions

### Screen Reader Support
- **Semantic HTML** structure
- **Live regions** for dynamic content
- **Descriptive text** for interactive elements

## Form Handling

### Validation Patterns
```tsx
// Client-side validation
const validation = validateContactForm(formData);
if (!validation.isValid) {
  setErrors(validation.errors);
  return;
}
```

### Error Handling
- **Field-level validation** with immediate feedback
- **Form-level validation** before submission
- **Server error handling** with user-friendly messages

## Testing Patterns

### Component Testing
```tsx
// Example test structure
describe('Button Component', () => {
  it('renders with correct variant', () => {
    render(<Button variant="primary">Click me</Button>);
    expect(screen.getByRole('button')).toHaveClass('primary-classes');
  });
});
```

### Hook Testing
```tsx
// Custom hook testing
const { result } = renderHook(() => useBlogData());
expect(result.current.loading).toBe(true);
```

## Development Workflow

### File Naming Conventions
- **PascalCase** for component files (Button.tsx)
- **camelCase** for utility files (utils.ts)
- **kebab-case** for page files (blog-post.tsx)

### Import Organization
```tsx
// 1. React imports
import { useState, useEffect } from 'react';

// 2. Third-party imports
import { motion } from 'framer-motion';

// 3. Internal imports
import { Button } from '@/components/ui';
import { useBlogData } from '@/hooks';
```

### Code Organization
- **Single responsibility** principle
- **Composition over inheritance**
- **Consistent error boundaries**
- **Proper TypeScript typing**
